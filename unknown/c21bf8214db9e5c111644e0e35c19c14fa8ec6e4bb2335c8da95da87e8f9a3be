-- 删除旧版客户分享表
-- 执行时间：2025-08-02
-- 说明：删除已废弃的 client_shares 表，该表已被 client_share_links 表替代

-- 检查表是否存在，如果存在则删除
DROP TABLE IF EXISTS client_shares;

-- 删除相关的权限记录（如果存在）
DELETE FROM role_permissions WHERE permission_id IN (
    SELECT id FROM permissions WHERE name IN ('client_share_view', 'client_share_create', 'client_share_delete')
);

DELETE FROM permissions WHERE name IN ('client_share_view', 'client_share_create', 'client_share_delete');

-- 输出完成信息
SELECT 'client_shares 表及相关权限已删除' as message;
