"""
客户相关模型
"""
from datetime import datetime, time
import json
from . import db


class Client(db.Model):
    """客户模型"""
    __tablename__ = 'clients'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    contact = db.Column(db.String(50))  # 联系人
    phone = db.Column(db.String(20))
    email = db.Column(db.String(100))
    need_review = db.Column(db.<PERSON>olean, default=True)  # 是否需要客户审核
    daily_content_count = db.Column(db.Integer, default=5)  # 每日展示数量
    display_start_time = db.Column(db.Time)  # 展示开始时间
    interval_min = db.Column(db.Integer, default=30)  # 最小间隔时间（分钟）
    interval_max = db.Column(db.Integer, default=120)  # 最大间隔时间（分钟）

    # 审核超时相关字段
    review_timeout_hours = db.Column(db.Integer, default=24)  # 审核超时小时数
    review_deadline_time = db.Column(db.Time, default=time(20, 0))  # 每日审核截止时间
    auto_approve_enabled = db.Column(db.Boolean, default=True)  # 是否启用自动通过

    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    status = db.Column(db.Boolean, default=True)  # True为启用，False为禁用
    ext_json = db.Column(db.Text)  # 扩展字段（JSON格式）

    # 默认值字段
    default_required_topics = db.Column(db.Text)  # 默认必选话题(JSON格式)
    default_random_topics = db.Column(db.Text)    # 默认随机话题(JSON格式)
    default_at_users = db.Column(db.Text)         # 默认@用户(JSON格式)
    default_location = db.Column(db.Text)         # 默认定位信息(JSON格式，支持多个)

    # 延迟删除相关字段
    delete_status = db.Column(db.Enum('active', 'deleting', 'delete_failed', 'deleted', name='delete_status_enum'), default='active')
    delete_started_at = db.Column(db.DateTime)
    delete_progress = db.Column(db.Text)  # JSON格式存储删除进度
    delete_error_message = db.Column(db.Text)
    
    # 关联
    tasks = db.relationship('Task', backref='client', lazy='dynamic')
    contents = db.relationship('Content', backref='client', lazy='dynamic')

    @property
    def default_required_topics_list(self):
        """获取默认必选话题列表"""
        if self.default_required_topics:
            try:
                return json.loads(self.default_required_topics)
            except:
                return []
        return []

    @default_required_topics_list.setter
    def default_required_topics_list(self, value):
        """设置默认必选话题列表"""
        if value:
            self.default_required_topics = json.dumps(value, ensure_ascii=False)
        else:
            self.default_required_topics = None

    @property
    def default_random_topics_list(self):
        """获取默认随机话题列表"""
        if self.default_random_topics:
            try:
                return json.loads(self.default_random_topics)
            except:
                return []
        return []

    @default_random_topics_list.setter
    def default_random_topics_list(self, value):
        """设置默认随机话题列表"""
        if value:
            self.default_random_topics = json.dumps(value, ensure_ascii=False)
        else:
            self.default_random_topics = None

    @property
    def default_at_users_list(self):
        """获取默认@用户列表"""
        if self.default_at_users:
            try:
                return json.loads(self.default_at_users)
            except:
                return []
        return []

    @default_at_users_list.setter
    def default_at_users_list(self, value):
        """设置默认@用户列表"""
        if value:
            self.default_at_users = json.dumps(value, ensure_ascii=False)
        else:
            self.default_at_users = None

    @property
    def default_location_list(self):
        """获取默认定位信息列表"""
        if self.default_location:
            try:
                # 尝试解析JSON格式
                parsed = json.loads(self.default_location)
                # 确保返回的是列表
                if isinstance(parsed, list):
                    return parsed
                else:
                    # 如果解析出来不是列表（比如单个字符串或数字），转换为列表
                    return [str(parsed)]
            except:
                # 如果不是JSON格式，兼容旧的单个定位数据
                return [self.default_location.strip()] if self.default_location.strip() else []
        return []

    @default_location_list.setter
    def default_location_list(self, value):
        """设置默认定位信息列表"""
        if value:
            self.default_location = json.dumps(value, ensure_ascii=False)
        else:
            self.default_location = None

    @property
    def ext_data(self):
        """获取扩展数据"""
        if self.ext_json:
            try:
                return json.loads(self.ext_json)
            except:
                return {}
        return {}
    
    @ext_data.setter
    def ext_data(self, value):
        """设置扩展数据"""
        if value:
            self.ext_json = json.dumps(value)
        else:
            self.ext_json = None

    def get_mark_defaults(self):
        """获取客户的所有标记默认值"""
        defaults = {}
        for mark_default in self.mark_defaults:
            defaults[mark_default.mark_name] = mark_default.values_list
        return defaults

    def set_mark_default(self, mark_name, values):
        """设置单个标记的默认值"""
        # 查找现有的标记默认值
        mark_default = self.mark_defaults.filter_by(mark_name=mark_name).first()

        if mark_default:
            # 更新现有的
            mark_default.values_list = values
            mark_default.updated_at = datetime.now()
        else:
            # 创建新的
            mark_default = ClientMarkDefault(
                client_id=self.id,
                mark_name=mark_name,
                values_list=values
            )
            db.session.add(mark_default)

        return mark_default

    def __repr__(self):
        return f'<Client {self.name}>'



class ClientShareLink(db.Model):
    """客户分享链接模型（新版）"""
    __tablename__ = 'client_share_links'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    share_key = db.Column(db.String(64), unique=True, nullable=False)
    access_key = db.Column(db.String(10))  # 访问密钥
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'))  # 限制访问的任务ID
    expires_at = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联关系
    client = db.relationship('Client', backref=db.backref('share_links', lazy='dynamic'))
    task = db.relationship('Task', backref=db.backref('share_links', lazy='dynamic'))

    def to_dict(self):
        """转换为字典格式"""
        # 安全地获取任务名称
        task_name = None
        if self.task_id and self.task:
            try:
                task_name = self.task.name
            except:
                task_name = None

        return {
            'id': self.id,
            'client_id': self.client_id,
            'share_key': self.share_key,
            'access_key': self.access_key,
            'task_id': self.task_id,
            'task_name': task_name,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_expired': self.is_expired
        }

    @property
    def is_expired(self):
        """检查是否已过期"""
        if not self.expires_at:
            return False
        return self.expires_at < datetime.now()

    @property
    def share_url(self):
        """获取分享URL"""
        try:
            from flask import url_for, request, has_request_context

            # 如果在请求上下文中，使用当前请求的域名信息
            if has_request_context():
                # 获取当前请求的协议和主机
                scheme = request.scheme
                host = request.host

                # 构建完整的URL
                relative_url = url_for('client_review.review_page', share_key=self.share_key)
                return f"{scheme}://{host}{relative_url}"
            else:
                # 如果不在请求上下文中，尝试使用 _external=True
                return url_for('client_review.review_page', share_key=self.share_key, _external=True)
        except:
            # 如果无法生成URL，返回一个基本的URL格式
            try:
                from flask import request
                if request:
                    base_url = request.url_root.rstrip('/')
                    return f"{base_url}/client-review/{self.share_key}"
            except:
                pass
            return f"/client-review/{self.share_key}"

    @property
    def days_remaining(self):
        """剩余天数"""
        if not self.expires_at:
            return None
        delta = self.expires_at - datetime.now()
        return max(0, delta.days)

    def __repr__(self):
        return f'<ClientShareLink {self.share_key}>'


class ClientMarkDefault(db.Model):
    """客户标记默认值模型"""
    __tablename__ = 'client_mark_defaults'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    mark_name = db.Column(db.String(100), nullable=False)  # 标记名称，如 "品牌"、"产品"
    default_values = db.Column(db.Text)  # JSON格式存储多个默认值
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 添加唯一约束
    __table_args__ = (
        db.UniqueConstraint('client_id', 'mark_name', name='unique_client_mark'),
    )

    # 关联关系
    client = db.relationship('Client', backref=db.backref('mark_defaults', lazy='dynamic', cascade='all, delete-orphan'))

    @property
    def values_list(self):
        """获取默认值列表"""
        if self.default_values:
            try:
                return json.loads(self.default_values)
            except:
                return []
        return []

    @values_list.setter
    def values_list(self, value):
        """设置默认值列表"""
        if value:
            self.default_values = json.dumps(value, ensure_ascii=False)
        else:
            self.default_values = None

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'client_id': self.client_id,
            'mark_name': self.mark_name,
            'default_values': self.values_list,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<ClientMarkDefault {self.client_id}-{self.mark_name}>'