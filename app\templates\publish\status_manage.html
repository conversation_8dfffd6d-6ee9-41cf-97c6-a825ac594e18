{% extends "base_simple.html" %}

{% block title %}发布状态管理 - 小红书文案管理系统{% endblock %}

{% block styles %}
    
    <!-- 自定义样式 -->
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 确保Bootstrap Icons正确加载 - 备用字体定义 */
        @font-face {
            font-family: "bootstrap-icons";
            src: url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff2") format("woff2"),
                 url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/fonts/bootstrap-icons.woff") format("woff");
        }

        /* 菜单图标样式强化 */
        .sidebar .nav-link i[class*="bi-"] {
            font-family: "bootstrap-icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
            display: inline-block !important;
            width: 1.2em !important;
            text-align: center !important;
        }

        /* 备用方案：如果Bootstrap Icons字体加载失败，使用Unicode符号 */
        .sidebar .nav-link i.bi-speedometer2::before { content: "📊" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-layer-group::before { content: "📋" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-people::before { content: "👥" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-pencil-square::before { content: "✏️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-clipboard-check::before { content: "✅" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-image::before { content: "🖼️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-check2-square::before { content: "✔️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-person-check::before { content: "👤" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-send::before { content: "📤" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-list-check::before { content: "📝" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-person-gear::before { content: "⚙️" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-gear::before { content: "🔧" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        .sidebar .nav-link i.bi-box-arrow-right::before { content: "🚪" !important; font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif !important; }
        
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 0.75rem 1rem;
            border-radius: 0;
        }

        .sidebar .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        .main-content {
            min-height: 100vh;
            padding: 0;
        }

        .top-bar {
            background-color: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            margin-bottom: 0;
        }

        .breadcrumb {
            margin-bottom: 0;
            background-color: transparent;
        }

        /* 图片相关样式 */
        #imageModal {
            z-index: 2000 !important;
        }

        #imageModal .modal-backdrop {
            z-index: 1999 !important;
        }

        .image-thumbnail:hover {
            transform: scale(1.05);
            transition: transform 0.2s ease;
        }

        .image-thumbnail[src*="data:image/svg+xml"] {
            border: 2px dashed #dee2e6;
        }

        /* 优先级设置按钮样式 */
        .priority-option {
            text-align: left;
            padding: 12px 16px;
        }

        .priority-option:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .priority-option.btn-danger,
        .priority-option.btn-warning,
        .priority-option.btn-secondary {
            color: white;
        }

        .priority-option small {
            font-size: 0.75rem;
            opacity: 0.8;
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mt-3">
                    <h3>发布状态管理</h3>
                    <!-- 筛选条件区域 -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <!-- 状态筛选 -->
                                <div class="btn-group" role="group" aria-label="发布状态筛选">
                                    <a href="{{ url_for('main_simple.publish_status_manage', status='all', client_id=request.args.get('client_id'), date_filter=request.args.get('date_filter'), priority=request.args.get('priority')) }}" class="btn btn-outline-secondary {% if current_status == 'all' %}active{% endif %}">全部</a>
                                    <a href="{{ url_for('main_simple.publish_status_manage', status='pending', client_id=request.args.get('client_id'), date_filter=request.args.get('date_filter'), priority=request.args.get('priority')) }}" class="btn btn-outline-primary {% if current_status == 'pending' %}active{% endif %}"><i class="bi bi-hourglass-split"></i> 待发布</a>
                                    <a href="{{ url_for('main_simple.publish_status_manage', status='publishing', client_id=request.args.get('client_id'), date_filter=request.args.get('date_filter'), priority=request.args.get('priority')) }}" class="btn btn-outline-info {% if current_status == 'publishing' %}active{% endif %}"><i class="bi bi-arrow-repeat"></i> 发布中</a>
                                    <a href="{{ url_for('main_simple.publish_status_manage', status='published', client_id=request.args.get('client_id'), date_filter=request.args.get('date_filter'), priority=request.args.get('priority')) }}" class="btn btn-outline-success {% if current_status == 'published' %}active{% endif %}"><i class="bi bi-check-circle"></i> 已发布</a>
                                    <a href="{{ url_for('main_simple.publish_status_manage', status='failed', client_id=request.args.get('client_id'), date_filter=request.args.get('date_filter'), priority=request.args.get('priority')) }}" class="btn btn-outline-danger {% if current_status == 'failed' %}active{% endif %}"><i class="bi bi-x-circle"></i> 发布失败</a>
                                    <a href="{{ url_for('main_simple.publish_status_manage', status='timeout', client_id=request.args.get('client_id'), date_filter=request.args.get('date_filter'), priority=request.args.get('priority')) }}" class="btn btn-outline-warning {% if current_status == 'timeout' %}active{% endif %}"><i class="bi bi-clock"></i> 发布超时</a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex gap-2">
                                    <select class="form-select form-select-sm" id="client-filter" onchange="applyFilters()">
                                        <option value="">全部客户</option>
                                        {% for client in clients %}
                                        <option value="{{ client.id }}" {{ 'selected' if request.args.get('client_id') == client.id|string else '' }}>
                                            {{ client.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <select class="form-select form-select-sm" id="date-filter" onchange="applyFilters()">
                                        <option value="">全部日期</option>
                                        <option value="today" {{ 'selected' if request.args.get('date_filter') == 'today' else '' }}>今天</option>
                                        <option value="yesterday" {{ 'selected' if request.args.get('date_filter') == 'yesterday' else '' }}>昨天</option>
                                        <option value="this_week" {{ 'selected' if request.args.get('date_filter') == 'this_week' else '' }}>本周</option>
                                        <option value="last_week" {{ 'selected' if request.args.get('date_filter') == 'last_week' else '' }}>上周</option>
                                    </select>
                                    <select class="form-select form-select-sm" id="priority-filter" onchange="applyFilters()">
                                        <option value="">全部优先级</option>
                                        <option value="high" {{ 'selected' if request.args.get('priority') == 'high' else '' }}>高优先级</option>
                                        <option value="normal" {{ 'selected' if request.args.get('priority') == 'normal' else '' }}>普通优先级</option>
                                        <option value="low" {{ 'selected' if request.args.get('priority') == 'low' else '' }}>低优先级</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <button class="btn btn-primary btn-sm" id="batch-reset-btn">批量重置为待发布</button>
                        <button class="btn btn-success btn-sm" id="batch-mark-published-btn">批量标记为已发布</button>
                        <button class="btn btn-danger btn-sm" id="batch-mark-failed-btn">批量标记为失败</button>
                        <button class="btn btn-secondary btn-sm" id="batch-delete-btn">批量删除</button>
                        <button class="btn btn-warning btn-sm" id="batch-set-priority-btn">批量设置优先级</button>
                    </div>
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all"></th>
                                <th>ID</th>
                                <th>标题</th>
                                <th>图片</th>
                                <th>客户</th>
                                <th>发布日期</th>
                                <th>优先级</th>
                                <th>发布状态</th>
                                <th>提示信息</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in content_data %}
                            <tr>
                                <td><input type="checkbox" class="content-checkbox" value="{{ item.content.id }}"></td>
                                <td><span class="badge bg-secondary">{{ item.content.id }}</span></td>
                                <td>{{ item.content.title }}</td>
                                <td>
                                    {% if item.images %}
                                        <span class="badge bg-primary">{{ item.image_count }} 张图片</span>
                                    {% else %}
                                        <span class="text-muted">无图片</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.content.client.name if item.content.client else '未知' }}</td>
                                <td>
                                    {% if item.content.display_date %}
                                        <span class="badge bg-info">{{ item.content.display_date.strftime('%Y-%m-%d') }}</span>
                                    {% else %}
                                        <span class="text-muted">未设置</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            {% if item.content.publish_priority == 'high' %}
                                                <span class="badge bg-danger">高</span>
                                            {% elif item.content.publish_priority == 'normal' %}
                                                <span class="badge bg-warning">普通</span>
                                            {% elif item.content.publish_priority == 'low' %}
                                                <span class="badge bg-secondary">低</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">未设置</span>
                                            {% endif %}
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-secondary"
                                                title="设置优先级"
                                                onclick="showPriorityModal({{ item.content.id }}, '{{ item.content.publish_priority or 'normal' }}', '{{ item.content.title|e }}')">
                                            <i class="bi bi-gear"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    {% if item.content.workflow_status == 'published' %}
                                        <span class="badge bg-success"><i class="bi bi-check-circle"></i> 已发布</span>
                                    {% elif item.content.workflow_status == 'publishing' %}
                                        <span class="badge bg-info"><i class="bi bi-arrow-repeat"></i> 发布中</span>
                                    {% elif item.content.workflow_status == 'publish_failed' %}
                                        <span class="badge bg-danger"><i class="bi bi-x-circle"></i> 发布失败</span>
                                    {% elif item.content.workflow_status == 'publish_timeout' %}
                                        <span class="badge bg-warning"><i class="bi bi-clock"></i> 发布超时</span>
                                    {% elif item.content.workflow_status == 'pending_publish' %}
                                        <span class="badge bg-secondary"><i class="bi bi-hourglass-split"></i> 待发布</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ item.content.workflow_status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.content.workflow_status == 'pending_publish' %}
                                        <span class="text-muted">等待发布</span>
                                    {% elif item.content.workflow_status == 'publishing' %}
                                        <span class="text-info">
                                            {% if item.content.publish_time %}
                                                {{ item.content.publish_time.strftime('%m-%d %H:%M') }} 开始发布
                                            {% else %}
                                                发布中...
                                            {% endif %}
                                        </span>
                                    {% elif item.content.workflow_status == 'published' %}
                                        <span class="text-success">
                                            {% if item.content.status_update_time %}
                                                {{ item.content.status_update_time.strftime('%m-%d %H:%M') }}
                                            {% endif %}
                                            {% if item.latest_publish_record and item.latest_publish_record.error_message %}
                                                {{ item.latest_publish_record.error_message }}
                                            {% endif %}
                                        </span>
                                    {% elif item.content.workflow_status == 'publish_failed' %}
                                        <span class="text-danger">
                                            {% if item.content.status_update_time %}
                                                {{ item.content.status_update_time.strftime('%m-%d %H:%M') }}
                                            {% endif %}
                                            {% if item.latest_publish_record and item.latest_publish_record.error_message %}
                                                {{ item.latest_publish_record.error_message }}
                                            {% endif %}
                                        </span>
                                    {% elif item.content.workflow_status == 'publish_timeout' %}
                                        <span class="text-warning">
                                            {% if item.content.publish_time %}
                                                {{ item.content.publish_time.strftime('%m-%d %H:%M') }} 开始，超时未回调
                                            {% else %}
                                                超时未回调
                                            {% endif %}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.content.workflow_status != 'published' %}
                                    <button class="btn btn-outline-warning btn-sm"
                                            onclick="showStatusResetModal({{ item.content.id }}, '{{ item.content.title|e }}', '{{ item.content.workflow_status }}')"
                                            title="重置状态">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-secondary btn-sm"
                                            data-content-id="{{ item.content.id }}"
                                            data-title="{{ item.content.title|e }}"
                                            data-client="{{ item.content.client.name if item.content.client else '未分配' }}"
                                            data-priority="{{ item.content.publish_priority }}"
                                            data-workflow-status="{{ item.content.workflow_status }}"
                                            data-created-at="{{ item.content.created_at.strftime('%Y-%m-%d %H:%M:%S') if item.content.created_at else '' }}"
                                            data-publish-time="{{ item.content.publish_time.strftime('%Y-%m-%d %H:%M:%S') if item.content.publish_time else '' }}"
                                            data-status-update-time="{{ item.content.status_update_time.strftime('%Y-%m-%d %H:%M:%S') if item.content.status_update_time else '' }}"
                                            data-image-count="{{ item.image_count }}"
                                            data-latest-message="{{ item.latest_publish_record.error_message|e if item.latest_publish_record and item.latest_publish_record.error_message else '' }}"
                                            data-publish-url="{{ item.latest_publish_record.publish_url|e if item.latest_publish_record and item.latest_publish_record.publish_url else '' }}"
                                            data-publish-platform="{{ item.latest_publish_record.platform|e if item.latest_publish_record and item.latest_publish_record.platform else '' }}"
                                            data-publish-account="{{ item.latest_publish_record.account|e if item.latest_publish_record and item.latest_publish_record.account else '' }}"
                                            data-topics="{{ item.content.topics|e if item.content.topics else '' }}"
                                            data-location="{{ item.content.location|e if item.content.location else '' }}"
                                            data-ext-json="{{ item.content.ext_json|e if item.content.ext_json else '' }}"
                                            onclick="viewDetailsFromButton(this)">详情</button>
                                    <button class="btn btn-outline-danger btn-sm"
                                            onclick="deleteContent({{ item.content.id }})"
                                            title="删除到回收站">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    <!-- 隐藏的文案内容和图片数据 -->
                                    <div class="d-none" id="content-{{ item.content.id }}">{{ item.content.content|e }}</div>
                                    <div class="d-none" id="images-{{ item.content.id }}">{{ item.images_json|e }}</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    
                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="分页导航">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main_simple.publish_status_manage', page=pagination.prev_num, status=current_status) }}">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main_simple.publish_status_manage', page=page_num, status=current_status) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main_simple.publish_status_manage', page=pagination.next_num, status=current_status) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
{% endblock %}

{% block scripts %}
    <!-- 页面脚本 -->
    <script>
        // 版本标识 - 2025-07-28-22:40 - 发布链接显示功能
        console.log('发布状态管理页面已加载 - 版本: 2025-07-28-22:40');

        // 全选/取消全选
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.content-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 批量操作函数
        function batchOperation(action) {
            const selectedIds = Array.from(document.querySelectorAll('.content-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedIds.length === 0) {
                showToast('请选择要操作的内容', 'warning');
                return;
            }

            if (!confirm(`确定要${action}选中的${selectedIds.length}个内容吗？`)) {
                return;
            }

            // 这里可以添加实际的批量操作逻辑
            console.log(`${action}操作，选中的ID:`, selectedIds);
        }

        // 批量标记为待发布
        function batchMarkPending() {
            const selectedIds = Array.from(document.querySelectorAll('.content-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedIds.length === 0) {
                showToast('请选择要操作的文案', 'warning');
                return;
            }

            if (!confirm(`确定要将选中的${selectedIds.length}篇文案标记为待发布吗？`)) {
                return;
            }

            fetch('/simple/api/publish-status/batch-mark-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ 
                    content_ids: selectedIds,
                    status: 'pending_publish'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('批量标记失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('批量标记失败，请重试', 'error');
            });
        }

        // 批量标记为已发布
        function batchMarkPublished() {
            const selectedIds = Array.from(document.querySelectorAll('.content-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedIds.length === 0) {
                showToast('请选择要操作的文案', 'warning');
                return;
            }

            if (!confirm(`确定要将选中的${selectedIds.length}篇文案标记为已发布吗？`)) {
                return;
            }

            fetch('/simple/api/publish-status/batch-mark-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ 
                    content_ids: selectedIds,
                    status: 'published'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('批量标记失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('批量标记失败，请重试', 'error');
            });
        }

        // 批量标记为失败
        function batchMarkFailed() {
            const selectedIds = Array.from(document.querySelectorAll('.content-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedIds.length === 0) {
                showToast('请选择要操作的文案', 'warning');
                return;
            }

            if (!confirm(`确定要将选中的${selectedIds.length}篇文案标记为发布失败吗？`)) {
                return;
            }

            fetch('/simple/api/publish-status/batch-mark-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ 
                    content_ids: selectedIds,
                    status: 'publish_failed'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('批量标记失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('批量标记失败，请重试', 'error');
            });
        }

        // 批量删除功能
        function batchDelete() {
            const selectedIds = Array.from(document.querySelectorAll('.content-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedIds.length === 0) {
                showToast('请选择要删除的文案', 'warning');
                return;
            }

            if (!confirm(`确定要删除选中的${selectedIds.length}篇文案吗？删除后将移动到回收站。`)) {
                return;
            }

            fetch('/simple/api/publish-status/batch-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ content_ids: selectedIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    // 延迟刷新页面，让用户看到Toast提示
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('批量删除失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('批量删除失败，请重试', 'error');
            });
        }

        // 单个删除功能
        function deleteContent(contentId) {
            if (!confirm('确定要删除这篇文案吗？删除后将移动到回收站。')) {
                return;
            }

            fetch(`/simple/api/contents/${contentId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: 'auto_supplement=false'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('文案已删除到回收站', 'success');
                    // 延迟刷新页面，让用户看到Toast提示
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('删除失败：' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                showToast('删除失败，请重试', 'error');
            });
        }

        // 显示Toast提示
        function showToast(message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : type === 'warning' ? 'bg-warning' : 'bg-primary';
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 显示状态重置模态框
        function showStatusResetModal(contentId, title, currentStatus) {
            // 设置模态框内容
            document.getElementById('resetContentTitle').textContent = title;

            // 状态映射
            const statusMap = {
                'pending_publish': '待发布',
                'publishing': '发布中',
                'published': '已发布',
                'publish_failed': '发布失败',
                'publish_timeout': '发布超时'
            };

            document.getElementById('resetCurrentStatus').textContent = statusMap[currentStatus] || currentStatus;

            // 绑定状态选择事件
            const statusOptions = document.querySelectorAll('.status-reset-option');
            statusOptions.forEach(option => {
                option.onclick = function() {
                    const newStatus = this.getAttribute('data-status');
                    resetContentStatus(contentId, newStatus);
                };
            });

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('statusResetModal'));
            modal.show();
        }

        // 重置文案状态
        function resetContentStatus(contentId, newStatus) {
            fetch(`/simple/api/contents/${contentId}/reset-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('状态重置成功', 'success');
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('statusResetModal'));
                    modal.hide();
                    // 延迟刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('状态重置失败：' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('状态重置失败:', error);
                showToast('状态重置失败，请重试', 'error');
            });
        }

        // 重试发布
        function retryPublish(contentId) {
            if (confirm('确定要重试发布此文案吗？')) {
                // 这里可以添加重试发布的逻辑
                console.log('重试发布文案ID:', contentId);
            }
        }

        // 开始发布（模拟第三方API调用）
        function startPublishing(contentId) {
            if (confirm('确定要开始发布此文案吗？')) {
                fetch(`/simple/api/publish/start-publishing/${contentId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('文案已开始发布');
                        location.reload();
                    } else {
                        alert('操作失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        }

        // 完成发布
        function completePublishing(contentId, status) {
            const action = status === 'success' ? '成功' : '失败';
            if (confirm(`确定要标记发布${action}吗？`)) {
                const data = { status: status };
                if (status === 'failed') {
                    const errorMsg = prompt('请输入失败原因：');
                    if (errorMsg !== null) {
                        data.error_message = errorMsg;
                    } else {
                        return;
                    }
                }

                fetch(`/simple/api/publish/complete/${contentId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('操作成功');
                        location.reload();
                    } else {
                        alert('操作失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        }

        // 标记超时
        function markTimeout(contentId) {
            if (confirm('确定要标记此文案为发布超时吗？')) {
                // 这里可以添加标记超时的逻辑
                console.log('标记超时文案ID:', contentId);
            }
        }

        // 查看详情（从按钮获取数据）
        function viewDetailsFromButton(button) {
            // 从按钮的data属性获取数据
            const contentId = button.getAttribute('data-content-id');
            const title = button.getAttribute('data-title');
            const clientName = button.getAttribute('data-client');
            const priority = button.getAttribute('data-priority');
            const workflowStatus = button.getAttribute('data-workflow-status');
            const createdAt = button.getAttribute('data-created-at');
            const publishTime = button.getAttribute('data-publish-time');
            const statusUpdateTime = button.getAttribute('data-status-update-time');
            const imageCount = button.getAttribute('data-image-count');
            const latestMessage = button.getAttribute('data-latest-message');
            const publishUrl = button.getAttribute('data-publish-url');
            const publishPlatform = button.getAttribute('data-publish-platform');
            const publishAccount = button.getAttribute('data-publish-account');
            const topicsJson = button.getAttribute('data-topics');
            const location = button.getAttribute('data-location');
            const extJson = button.getAttribute('data-ext-json');

            // 调试信息
            console.log('发布链接数据:', {
                publishUrl: publishUrl,
                publishPlatform: publishPlatform,
                publishAccount: publishAccount
            });

            // 从隐藏div获取文案内容
            const contentDiv = document.getElementById('content-' + contentId);
            const content = contentDiv ? contentDiv.textContent : '';

            // 从隐藏div获取图片数据
            const imagesDiv = document.getElementById('images-' + contentId);
            let images = [];
            if (imagesDiv && imagesDiv.textContent) {
                try {
                    images = JSON.parse(imagesDiv.textContent);
                } catch (e) {
                    console.error('解析图片数据失败:', e);
                }
            }

            // 解析话题数据
            let topics = [];
            if (topicsJson) {
                try {
                    topics = JSON.parse(topicsJson);
                } catch (e) {
                    console.error('解析话题数据失败:', e);
                    topics = [];
                }
            }

            // 解析艾特用户数据
            let atUsers = [];
            if (extJson) {
                try {
                    const extData = JSON.parse(extJson);
                    atUsers = extData.at_users || [];
                } catch (e) {
                    console.error('解析扩展数据失败:', e);
                    atUsers = [];
                }
            }

            console.log('查看详情文案ID:', contentId, '图片数量:', images.length, '话题数量:', topics.length, '艾特用户数量:', atUsers.length);

            // 显示弹窗
            const modal = new bootstrap.Modal(document.getElementById('detailModal'));
            modal.show();

            // 直接显示详情内容
            displayContentDetailsSimple({
                id: contentId,
                title: title,
                content: content,
                client_name: clientName,
                priority: priority,
                workflow_status: workflowStatus,
                created_at: createdAt,
                publish_time: publishTime,
                status_update_time: statusUpdateTime,
                image_count: parseInt(imageCount) || 0,
                latest_message: latestMessage,
                publish_url: publishUrl,
                publish_platform: publishPlatform,
                publish_account: publishAccount,
                images: images,
                topics: topics,
                location: location,
                at_users: atUsers
            });
        }

        // 显示文案详情（简化版）
        function displayContentDetailsSimple(content) {
            const modalBody = document.getElementById('detailModalBody');

            // 调试信息
            console.log('displayContentDetailsSimple 接收到的数据:', {
                publish_url: content.publish_url,
                publish_platform: content.publish_platform,
                publish_account: content.publish_account
            });

            // 构建详情HTML - 使用左右布局
            let detailsHtml = `
                <div class="row">
                    <!-- 左侧：基本信息和文案内容 -->
                    <div class="col-md-${content.images && content.images.length > 0 ? '8' : '12'}">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-info-circle"></i> 基本信息</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>ID:</strong></td><td><span class="badge bg-secondary">${content.id}</span></td></tr>
                                    <tr><td><strong>标题:</strong></td><td>${content.title}</td></tr>
                                    <tr><td><strong>客户:</strong></td><td>${content.client_name || '未分配'}</td></tr>
                                    <tr><td><strong>优先级:</strong></td><td>
                                        ${content.priority === 'high' ? '<span class="badge bg-danger">高</span>' :
                                          content.priority === 'normal' ? '<span class="badge bg-warning">中</span>' :
                                          '<span class="badge bg-secondary">低</span>'}
                                    </td></tr>
                                    <tr><td><strong>创建时间:</strong></td><td>${content.created_at || '-'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-gear"></i> 发布状态</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>工作流状态:</strong></td><td>
                                        ${getStatusBadge(content.workflow_status)}
                                    </td></tr>
                                    <tr><td><strong>发布状态:</strong></td><td>${content.publish_status || '-'}</td></tr>
                                    <tr><td><strong>获取时间:</strong></td><td>${content.publish_time || '-'}</td></tr>
                                    <tr><td><strong>状态更新:</strong></td><td>${content.status_update_time || '-'}</td></tr>
                                    <tr><td><strong>发布平台:</strong></td><td>${content.publish_platform || '-'}</td></tr>
                                    <tr><td><strong>发布账号:</strong></td><td>${content.publish_account || '-'}</td></tr>
                                    <tr><td><strong>发布链接:</strong></td><td>
                                        ${content.publish_url ?
                                            `<a href="${content.publish_url}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-box-arrow-up-right"></i> 查看发布内容
                                            </a>` :
                                            '<span class="text-muted">无链接</span>'
                                        }
                                    </td></tr>
                                    <tr><td><strong>图片数量:</strong></td><td>
                                        ${content.image_count > 0 ?
                                          `<span class="badge bg-primary">${content.image_count} 张图片</span>` :
                                          '<span class="text-muted">无图片</span>'}
                                    </td></tr>
                                </table>
                            </div>
                        </div>

                        <hr>

                        <div class="row">
                            <div class="col-12">
                                <h6><i class="bi bi-file-text"></i> 文案内容</h6>
                                <div class="card">
                                    <div class="card-body">
                                        <pre style="white-space: pre-wrap; font-family: inherit; max-height: 300px; overflow-y: auto;">${content.content}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- 话题、艾特用户、地址信息 -->
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="bi bi-tags"></i> 话题标签</h6>
                                <div class="mb-3">
                                    ${content.topics && content.topics.length > 0 ?
                                        content.topics.map(topic => `<span class="badge bg-primary me-1 mb-1">#${topic}</span>`).join('') :
                                        '<span class="text-muted">无话题标签</span>'
                                    }
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="bi bi-at"></i> 艾特用户</h6>
                                <div class="mb-3">
                                    ${content.at_users && content.at_users.length > 0 ?
                                        content.at_users.map(user => `<span class="badge bg-warning text-dark me-1 mb-1">@${user.replace('@', '')}</span>`).join('') :
                                        '<span class="text-muted">无艾特用户</span>'
                                    }
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="bi bi-geo-alt"></i> 地址信息</h6>
                                <div class="mb-3">
                                    ${content.location ?
                                        `<span class="badge bg-success"><i class="bi bi-geo-alt-fill"></i> ${content.location}</span>` :
                                        '<span class="text-muted">无地址信息</span>'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
            `;

            // 右侧：图片展示
            if (content.images && content.images.length > 0) {
                detailsHtml += `
                    <div class="col-md-4">
                        <h6><i class="bi bi-images"></i> 关联图片 (${content.images.length}张)</h6>
                        <div class="row g-2">
                `;

                content.images.forEach((image, index) => {
                    detailsHtml += `
                        <div class="col-6">
                            <div class="card">
                                <img src="${image.url}"
                                     class="card-img-top image-thumbnail"
                                     style="height: 120px; object-fit: cover; cursor: pointer;"
                                     alt="图片${image.order}"
                                     onclick="enlargeImage('${image.url}', '图片 ${image.order}')"
                                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+aXoOazleaYvuekujwvdGV4dD48L3N2Zz4=';">
                                <div class="card-body p-2">
                                    <small class="text-muted">顺序: ${image.order}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });

                detailsHtml += `
                        </div>
                        <div class="mt-2">
                            <small class="text-muted"><i class="bi bi-zoom-in"></i> 点击图片可放大查看</small>
                        </div>
                    </div>
                `;
            }

            detailsHtml += `</div>`;

            // 调试：输出生成的HTML
            console.log('生成的详情HTML:', detailsHtml);

            // 如果有最新的提示信息
            if (content.latest_message) {
                detailsHtml += `
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6><i class="bi bi-chat-dots"></i> 最新提示信息</h6>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> ${content.latest_message}
                            </div>
                        </div>
                    </div>
                `;
            }

            modalBody.innerHTML = detailsHtml;
        }

        // 显示文案详情
        function displayContentDetails(content) {
            const modalBody = document.getElementById('detailModalBody');

            // 构建详情HTML
            let detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-info-circle"></i> 基本信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>ID:</strong></td><td><span class="badge bg-secondary">${content.id}</span></td></tr>
                            <tr><td><strong>标题:</strong></td><td>${content.title}</td></tr>
                            <tr><td><strong>客户:</strong></td><td>${content.client_name || '未分配'}</td></tr>
                            <tr><td><strong>优先级:</strong></td><td>
                                ${content.priority === 'high' ? '<span class="badge bg-danger">高</span>' :
                                  content.priority === 'normal' ? '<span class="badge bg-warning">中</span>' :
                                  '<span class="badge bg-secondary">低</span>'}
                            </td></tr>
                            <tr><td><strong>创建时间:</strong></td><td>${content.created_at || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-gear"></i> 发布状态</h6>
                        <table class="table table-sm">
                            <tr><td><strong>工作流状态:</strong></td><td>
                                ${getStatusBadge(content.workflow_status)}
                            </td></tr>
                            <tr><td><strong>发布状态:</strong></td><td>${content.publish_status || '-'}</td></tr>
                            <tr><td><strong>获取时间:</strong></td><td>${content.publish_time || '-'}</td></tr>
                            <tr><td><strong>状态更新:</strong></td><td>${content.status_update_time || '-'}</td></tr>
                            <tr><td><strong>发布平台:</strong></td><td>${content.publish_platform || '-'}</td></tr>
                            <tr><td><strong>发布账号:</strong></td><td>${content.publish_account || '-'}</td></tr>
                            <tr><td><strong>发布链接:</strong></td><td>
                                ${content.publish_url ?
                                    `<a href="${content.publish_url}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-box-arrow-up-right"></i> 查看发布内容
                                    </a>` :
                                    '<span class="text-muted">无链接</span>'
                                }
                            </td></tr>
                        </table>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-12">
                        <h6><i class="bi bi-file-text"></i> 文案内容</h6>
                        <div class="card">
                            <div class="card-body">
                                <pre style="white-space: pre-wrap; font-family: inherit;">${content.content}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 如果有图片信息
            if (content.images && content.images.length > 0) {
                detailsHtml += `
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6><i class="bi bi-images"></i> 关联图片 (${content.images.length}张)</h6>
                            <div class="row">
                `;

                content.images.forEach(image => {
                    detailsHtml += `
                        <div class="col-md-3 mb-2">
                            <div class="card">
                                <img src="${image.url}" class="card-img-top" style="height: 120px; object-fit: cover;" alt="图片">
                                <div class="card-body p-2">
                                    <small class="text-muted">顺序: ${image.order}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });

                detailsHtml += `
                            </div>
                        </div>
                    </div>
                `;
            }

            // 如果有发布记录
            if (content.publish_records && content.publish_records.length > 0) {
                detailsHtml += `
                    <hr>
                    <div class="row">
                        <div class="col-12">
                            <h6><i class="bi bi-clock-history"></i> 发布记录历史</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>状态</th>
                                            <th>平台</th>
                                            <th>账号</th>
                                            <th>链接</th>
                                            <th>提示信息</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;

                content.publish_records.forEach(record => {
                    detailsHtml += `
                        <tr>
                            <td><small>${record.publish_time}</small></td>
                            <td>
                                ${record.status === 'success' ? '<span class="badge bg-success">成功</span>' :
                                  '<span class="badge bg-danger">失败</span>'}
                            </td>
                            <td><small>${record.platform || '-'}</small></td>
                            <td><small>${record.account || '-'}</small></td>
                            <td>
                                ${record.publish_url ?
                                  `<a href="${record.publish_url}" target="_blank" class="btn btn-sm btn-outline-primary">查看</a>` :
                                  '-'}
                            </td>
                            <td><small>${record.error_message || '-'}</small></td>
                        </tr>
                    `;
                });

                detailsHtml += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }

            modalBody.innerHTML = detailsHtml;

            // 调试：检查设置后的HTML
            console.log('设置后的modalBody.innerHTML:', modalBody.innerHTML);
        }

        // 获取状态徽章
        function getStatusBadge(status) {
            const statusMap = {
                'pending_publish': '<span class="badge bg-secondary"><i class="bi bi-hourglass-split"></i> 待发布</span>',
                'publishing': '<span class="badge bg-info"><i class="bi bi-arrow-repeat"></i> 发布中</span>',
                'published': '<span class="badge bg-success"><i class="bi bi-check-circle"></i> 已发布</span>',
                'publish_failed': '<span class="badge bg-danger"><i class="bi bi-x-circle"></i> 发布失败</span>',
                'publish_timeout': '<span class="badge bg-warning"><i class="bi bi-clock"></i> 发布超时</span>'
            };
            return statusMap[status] || `<span class="badge bg-secondary">${status}</span>`;
        }

        // 放大图片
        function enlargeImage(imageUrl, imageTitle) {
            console.log('放大图片:', imageUrl, imageTitle);

            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            const enlargedImage = document.getElementById('enlargedImage');
            const imageInfo = document.getElementById('imageInfo');

            enlargedImage.src = imageUrl;
            imageInfo.textContent = imageTitle;

            // 确保图片放大弹窗在最顶层
            const imageModal = document.getElementById('imageModal');
            imageModal.style.zIndex = '2000';

            modal.show();

            console.log('图片放大弹窗已显示');
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            // 创建提示框元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // 添加到页面
            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 获取选中的文案ID列表
        function getSelectedContentIds() {
            const checkboxes = document.querySelectorAll('.content-checkbox:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }

        // 应用筛选条件
        function applyFilters() {
            const clientId = document.getElementById('client-filter').value;
            const dateFilter = document.getElementById('date-filter').value;
            const priority = document.getElementById('priority-filter').value;
            const currentStatus = '{{ current_status or "" }}';

            // 构建URL参数
            const params = new URLSearchParams();

            if (currentStatus) {
                params.set('status', currentStatus);
            }

            if (clientId) {
                params.set('client_id', clientId);
            }

            if (dateFilter) {
                params.set('date_filter', dateFilter);
            }

            if (priority) {
                params.set('priority', priority);
            }

            // 跳转到新的URL
            const baseUrl = '{{ url_for("main_simple.publish_status_manage") }}';
            const newUrl = params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
            window.location.href = newUrl;
        }

        // 显示优先级设置弹窗
        let currentContentId = null;

        function showPriorityModal(contentId, currentPriority, contentTitle) {
            console.log('显示优先级设置弹窗:', contentId, currentPriority, contentTitle);

            currentContentId = contentId;

            // 设置弹窗标题
            document.getElementById('priorityContentTitle').textContent = contentTitle;

            // 重置所有按钮状态
            document.querySelectorAll('.priority-option').forEach(btn => {
                btn.classList.remove('btn-danger', 'btn-warning', 'btn-secondary');
                btn.classList.add('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
            });

            // 高亮当前优先级
            const currentBtn = document.querySelector(`[data-priority="${currentPriority}"]`);
            if (currentBtn) {
                currentBtn.classList.remove('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
                if (currentPriority === 'high') {
                    currentBtn.classList.add('btn-danger');
                } else if (currentPriority === 'normal') {
                    currentBtn.classList.add('btn-warning');
                } else {
                    currentBtn.classList.add('btn-secondary');
                }
            }

            // 显示弹窗
            const modal = new bootstrap.Modal(document.getElementById('priorityModal'));
            modal.show();
        }

        // 更新单个文案优先级
        function updatePriority(contentId, priority) {
            console.log('更新优先级:', contentId, priority);

            fetch('/simple/api/update-priority', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    content_id: contentId,
                    priority: priority
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('优先级更新成功', 'success');

                    // 关闭弹窗
                    const modal = bootstrap.Modal.getInstance(document.getElementById('priorityModal'));
                    if (modal) {
                        modal.hide();
                    }

                    // 刷新页面以更新显示
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('优先级更新失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('网络错误，请重试', 'danger');
            });
        }

        // 绑定优先级选择按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            // 单个优先级设置按钮
            document.querySelectorAll('.priority-option').forEach(btn => {
                btn.addEventListener('click', function() {
                    const priority = this.getAttribute('data-priority');
                    if (currentContentId) {
                        updatePriority(currentContentId, priority);
                    }
                });
            });

            // 批量优先级设置按钮
            document.querySelectorAll('.batch-priority-option').forEach(btn => {
                btn.addEventListener('click', function() {
                    const priority = this.getAttribute('data-priority');
                    batchSetPriority(priority);
                });
            });
        });

        // 显示批量优先级设置弹窗
        function showBatchPriorityModal() {
            const selectedIds = getSelectedContentIds();

            if (selectedIds.length === 0) {
                showAlert('请先选择要设置的文案', 'warning');
                return;
            }

            // 更新选中文案数量
            document.getElementById('batchSelectedCount').textContent = `${selectedIds.length} 篇文案`;

            // 显示弹窗
            const modal = new bootstrap.Modal(document.getElementById('batchPriorityModal'));
            modal.show();
        }

        // 批量设置优先级
        function batchSetPriority(priority) {
            const selectedIds = getSelectedContentIds();

            if (selectedIds.length === 0) {
                showAlert('请先选择要设置的文案', 'warning');
                return;
            }

            if (!priority) {
                showAlert('请选择优先级', 'warning');
                return;
            }

            // 关闭弹窗
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchPriorityModal'));
            if (modal) {
                modal.hide();
            }

            // 显示确认对话框
            if (!confirm(`确定要将选中的 ${selectedIds.length} 篇文案的优先级设置为 ${getPriorityText(priority)} 吗？`)) {
                return;
            }

            fetch('/simple/api/batch-update-priority', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    content_ids: selectedIds,
                    priority: priority
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`成功设置 ${data.updated_count} 篇文案的优先级为 ${getPriorityText(priority)}`, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('批量设置失败: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('网络错误，请重试', 'danger');
            });
        }

        // 获取优先级文字
        function getPriorityText(priority) {
            const priorityMap = {
                'high': '高',
                'normal': '普通',
                'low': '低'
            };
            return priorityMap[priority] || priority;
        }

        // 绑定批量操作按钮
        document.getElementById('batch-reset-btn').addEventListener('click', batchMarkPending);
        document.getElementById('batch-mark-published-btn').addEventListener('click', batchMarkPublished);
        document.getElementById('batch-mark-failed-btn').addEventListener('click', batchMarkFailed);
        document.getElementById('batch-delete-btn').addEventListener('click', batchDelete);
        document.getElementById('batch-set-priority-btn').addEventListener('click', showBatchPriorityModal);
    </script>

    <!-- 详情弹窗 -->
    <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel">文案详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detailModalBody">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片放大弹窗 -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true" style="z-index: 2000;">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content bg-transparent border-0">
                <div class="modal-header border-0 pb-0">
                    <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center p-0">
                    <img id="enlargedImage" src="" class="img-fluid rounded" alt="放大图片" style="max-height: 80vh;" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+aXoOazleaYvuekujwvdGV4dD48L3N2Zz4=';">
                    <div class="mt-2">
                        <small class="text-white bg-dark px-2 py-1 rounded" id="imageInfo">图片信息</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 单个优先级设置弹窗 -->
    <div class="modal fade" id="priorityModal" tabindex="-1" aria-labelledby="priorityModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="priorityModalLabel">
                        <i class="bi bi-flag"></i> 设置优先级
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">文案标题：</label>
                        <p class="text-muted small" id="priorityContentTitle">-</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">选择优先级：</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-danger priority-option" data-priority="high">
                                <i class="bi bi-arrow-up-circle"></i> 高优先级
                                <small class="d-block text-muted">优先发布</small>
                            </button>
                            <button type="button" class="btn btn-outline-warning priority-option" data-priority="normal">
                                <i class="bi bi-dash-circle"></i> 普通优先级
                                <small class="d-block text-muted">正常发布</small>
                            </button>
                            <button type="button" class="btn btn-outline-secondary priority-option" data-priority="low">
                                <i class="bi bi-arrow-down-circle"></i> 低优先级
                                <small class="d-block text-muted">延后发布</small>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量优先级设置弹窗 -->
    <div class="modal fade" id="batchPriorityModal" tabindex="-1" aria-labelledby="batchPriorityModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchPriorityModalLabel">
                        <i class="bi bi-flag-fill"></i> 批量设置优先级
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">选中文案数量：</label>
                        <p class="text-primary fw-bold" id="batchSelectedCount">0 篇文案</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">选择优先级：</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-danger batch-priority-option" data-priority="high">
                                <i class="bi bi-arrow-up-circle"></i> 高优先级
                                <small class="d-block text-muted">优先发布</small>
                            </button>
                            <button type="button" class="btn btn-outline-warning batch-priority-option" data-priority="normal">
                                <i class="bi bi-dash-circle"></i> 普通优先级
                                <small class="d-block text-muted">正常发布</small>
                            </button>
                            <button type="button" class="btn btn-outline-secondary batch-priority-option" data-priority="low">
                                <i class="bi bi-arrow-down-circle"></i> 低优先级
                                <small class="d-block text-muted">延后发布</small>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态重置弹窗 -->
    <div class="modal fade" id="statusResetModal" tabindex="-1" aria-labelledby="statusResetModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statusResetModalLabel">
                        <i class="bi bi-arrow-clockwise"></i> 重置状态
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">文案标题：</label>
                        <p class="text-muted small" id="resetContentTitle">-</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">当前状态：</label>
                        <p class="text-info" id="resetCurrentStatus">-</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">重置为：</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-info status-reset-option" data-status="pending_publish">
                                <i class="bi bi-clock"></i> 待发布
                                <small class="d-block text-muted">重新排队等待发布</small>
                            </button>
                            <button type="button" class="btn btn-outline-warning status-reset-option" data-status="publish_failed">
                                <i class="bi bi-x-circle"></i> 发布失败
                                <small class="d-block text-muted">标记为发布失败状态</small>
                            </button>
                            <button type="button" class="btn btn-outline-danger status-reset-option" data-status="publish_timeout">
                                <i class="bi bi-exclamation-triangle"></i> 发布超时
                                <small class="d-block text-muted">标记为发布超时状态</small>
                            </button>
                            <button type="button" class="btn btn-outline-success status-reset-option" data-status="published">
                                <i class="bi bi-check-circle"></i> 已发布
                                <small class="d-block text-muted">标记为发布成功状态</small>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}