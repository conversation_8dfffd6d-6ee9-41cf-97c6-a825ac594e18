{% extends "base_simple.html" %}

{% block title %}{{ client.name }} - 客户详情{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ url_for('main_simple.clients') }}" class="text-decoration-none">
                    <i class="bi bi-people"></i> 客户管理
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="bi bi-person"></i> {{ client.name }}
            </li>
        </ol>
    </nav>

    <!-- 客户基本信息 -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="bi bi-person-circle"></i> {{ client.name }} - 详细信息
                </h4>
                <a href="{{ url_for('main_simple.clients') }}" class="btn btn-light btn-sm">
                    <i class="bi bi-arrow-left"></i> 返回客户管理
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>客户名称：</strong>{{ client.name }}
                </div>
                <div class="col-md-3">
                    <strong>联系方式：</strong>{{ client.contact or '-' }}
                </div>
                <div class="col-md-3">
                    <strong>创建时间：</strong>{{ client.created_at.strftime('%Y-%m-%d %H:%M') if client.created_at else '-' }}
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="row mb-4" id="statsCards">
        <div class="col-md-3">
            <div class="card text-center border-primary">
                <div class="card-body">
                    <h5 class="card-title text-primary" id="totalCount">-</h5>
                    <p class="card-text small">总数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-warning">
                <div class="card-body">
                    <h5 class="card-title text-warning" id="pendingReviewCount">-</h5>
                    <p class="card-text small">待初审</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-info">
                <div class="card-body">
                    <h5 class="card-title text-info" id="pendingImageCount">-</h5>
                    <p class="card-text small">图片待提交</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-secondary">
                <div class="card-body">
                    <h5 class="card-title text-secondary" id="finalReviewCount">-</h5>
                    <p class="card-text small">最终审核</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center border-purple">
                <div class="card-body">
                    <h5 class="card-title text-purple" id="clientPendingCount">-</h5>
                    <p class="card-text small">客户待审核</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-dark">
                <div class="card-body">
                    <h5 class="card-title text-dark" id="pendingPublishCount">-</h5>
                    <p class="card-text small">待发布</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-success">
                <div class="card-body">
                    <h5 class="card-title text-success" id="publishedCount">-</h5>
                    <p class="card-text small">已发布</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center border-danger">
                <div class="card-body">
                    <h5 class="card-title text-danger" id="failedCount">-</h5>
                    <p class="card-text small">发布失败</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签页导航 -->
    <ul class="nav nav-tabs mb-4" id="clientDetailTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="contents-tab" data-bs-toggle="tab" data-bs-target="#contents" type="button" role="tab">
                <i class="bi bi-file-text"></i> 文章管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab">
                <i class="bi bi-list-task"></i> 任务管理
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="recycle-tab" data-bs-toggle="tab" data-bs-target="#recycle" type="button" role="tab">
                <i class="bi bi-trash"></i> 回收站
            </button>
        </li>
    </ul>

    <!-- 标签页内容 -->
    <div class="tab-content" id="clientDetailTabContent">
        <!-- 文章管理标签页 -->
        <div class="tab-pane fade show active" id="contents" role="tabpanel">
            <!-- 筛选工具栏 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-funnel"></i> 数据筛选
                    </h5>
                </div>
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-2">
                    <label for="taskFilter" class="form-label">任务</label>
                    <select class="form-select" id="taskFilter" name="task_id">
                        <option value="">全部任务</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="batchFilter" class="form-label">批次</label>
                    <select class="form-select" id="batchFilter" name="batch_id">
                        <option value="">全部批次</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="statusFilter" class="form-label">状态</label>
                    <select class="form-select" id="statusFilter" name="status">
                        <option value="">全部状态</option>
                        <option value="pending_review">待初审</option>
                        <option value="pending_image">图片待提交</option>
                        <option value="final_review">最终审核</option>
                        <option value="client_pending">客户待审核</option>
                        <option value="pending_publish">待发布</option>
                        <option value="published">已发布</option>
                        <option value="publish_failed">发布失败</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="dateFrom" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="dateFrom" name="date_from">
                </div>
                <div class="col-md-2">
                    <label for="dateTo" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="dateTo" name="date_to">
                </div>
                <div class="col-md-2">
                    <label for="searchText" class="form-label">关键词</label>
                    <input type="text" class="form-control" id="searchText" name="search" placeholder="搜索标题">
                </div>
                <div class="col-12">
                    <button type="button" class="btn btn-primary" onclick="applyFilters()">
                        <i class="bi bi-search"></i> 筛选
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </button>
                    <button type="button" class="btn btn-success" onclick="exportData()">
                        <i class="bi bi-download"></i> 导出
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 内容列表 -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-3">
                        <i class="bi bi-list-ul"></i> 内容列表
                    </h5>
                    <span id="resultCount" class="badge bg-info">加载中...</span>
                </div>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-outline-danger btn-sm" id="batchDeleteBtn" onclick="showBatchDeleteModal()" disabled>
                        <i class="bi bi-trash"></i> 批量删除
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="contentTable">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th width="80">任务ID</th>
                            <th>任务名称</th>
                            <th>批次名称</th>
                            <th>标题</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>审核时间</th>
                            <th>发布时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="contentTableBody">
                        <tr>
                            <td colspan="10" class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2">正在加载数据...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="内容分页" id="paginationNav" style="display: none;">
                <ul class="pagination justify-content-center" id="pagination">
                </ul>
            </nav>
        </div>
    </div>
        </div>

        <!-- 任务管理标签页 -->
        <div class="tab-pane fade" id="tasks" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-list-task"></i> 任务列表
                        </h5>
                        <div>
                            <button type="button" class="btn btn-primary btn-sm" onclick="createNewTask()">
                                <i class="bi bi-plus"></i> 新建任务
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="batchDeleteTasks()" id="batchDeleteTasksBtn" style="display: none;">
                                <i class="bi bi-trash"></i> 批量删除
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="tasksTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAllTasks" onchange="toggleSelectAllTasks()">
                                    </th>
                                    <th width="80">ID</th>
                                    <th>任务名称</th>
                                    <th>描述</th>
                                    <th>状态</th>
                                    <th>文章数</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tasksTableBody">
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载任务列表...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 回收站标签页 -->
        <div class="tab-pane fade" id="recycle" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-trash"></i> 回收站
                        </h5>
                        <div>
                            <button type="button" class="btn btn-warning btn-sm" onclick="batchRestoreContents()" id="batchRestoreBtn" style="display: none;">
                                <i class="bi bi-arrow-clockwise"></i> 批量恢复
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="batchPermanentDelete()" id="batchPermanentDeleteBtn" style="display: none;">
                                <i class="bi bi-trash-fill"></i> 永久删除
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearRecycleBin()">
                                <i class="bi bi-trash3"></i> 清空回收站
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 统计信息 -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                回收站中共有 <span id="recycleCount">0</span> 篇已删除的文章，
                                <span class="text-muted">可以选择恢复或永久删除</span>
                            </div>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>恢复说明：</strong>
                                恢复的文章将重置为 <span class="badge bg-warning text-dark">草稿状态</span>，
                                需要重新进入审核流程（初审 → 图片上传 → 最终审核 → 客户审核 → 发布）
                            </div>
                        </div>
                    </div>

                    <!-- 文章列表 -->
                    <div class="table-responsive">
                        <table class="table table-hover" id="recycleTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAllRecycle" onchange="toggleSelectAllRecycle()">
                                    </th>
                                    <th width="80">任务ID</th>
                                    <th>任务名称</th>
                                    <th>批次名称</th>
                                    <th>标题</th>
                                    <th>删除时间</th>
                                    <th width="150">操作</th>
                                </tr>
                            </thead>
                            <tbody id="recycleTableBody">
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2">正在加载回收站数据...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="回收站分页" id="recyclePaginationNav" style="display: none;">
                        <ul class="pagination justify-content-center" id="recyclePagination">
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>已选择 <span id="selectedCount">0</span> 项内容</p>
                <div class="mb-3">
                    <label for="batchStatus" class="form-label">更新状态为：</label>
                    <select class="form-select" id="batchStatus">
                        <option value="">请选择状态</option>
                        <option value="first_reviewed">初审通过</option>
                        <option value="pending_final_review">待最终审核</option>
                        <option value="client_approved">客户已通过</option>
                        <option value="client_rejected">客户已拒绝</option>
                        <option value="pending_publish">待发布</option>
                        <option value="published">已发布</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeBatchAction()">确认更新</button>
            </div>
        </div>
    </div>
</div>

<!-- 新建/编辑任务模态框 -->
<div class="modal fade" id="taskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskModalTitle">新建任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="taskForm">
                    <div class="mb-3">
                        <label for="taskName" class="form-label">任务名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="taskName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="taskDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskStatus" class="form-label">状态</label>
                        <select class="form-select" id="taskStatus" name="status">
                            <option value="processing">进行中</option>
                            <option value="completed">已完成</option>
                            <option value="paused">已暂停</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveTask()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除任务确认模态框 -->
<div class="modal fade" id="deleteTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除任务确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>警告：</strong>删除任务将会：
                </div>
                <ul class="mb-3">
                    <li>删除该任务下的所有文章</li>
                    <li>删除相关的图片文件</li>
                    <li>删除相关的批次数据</li>
                    <li><strong>此操作不可恢复！</strong></li>
                </ul>
                <p>确定要删除任务 "<span id="deleteTaskName"></span>" 吗？</p>
                <div id="taskDeleteStats" class="text-muted small"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteTask()">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 文章内容查看模态框 -->
<div class="modal fade" id="contentViewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-text"></i> 文章详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="contentViewBody">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<style>
/* 状态标签样式 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending_review { background-color: #ffc107; color: #000; }
.status-pending_image { background-color: #17a2b8; }
.status-final_review { background-color: #6c757d; }
.status-pending_client_review { background-color: #6f42c1; }
.status-pending_publish { background-color: #0dcaf0; color: #000; }
.status-published { background-color: #198754; }
.status-publish_failed { background-color: #dc3545; }

/* 兼容旧状态样式 */
.status-draft { background-color: #ffc107; color: #000; }
.status-first_reviewed { background-color: #17a2b8; }
.status-image_uploaded { background-color: #17a2b8; }
.status-ready_to_publish { background-color: #0dcaf0; color: #000; }

/* 表格样式优化 */
.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* 筛选表单样式 */
.form-label {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

/* 统计卡片动画 */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

/* 紫色边框和文字样式 */
.border-purple {
    border-color: #6f42c1 !important;
}

.text-purple {
    color: #6f42c1 !important;
}
</style>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDeleteModalLabel">
                    <i class="bi bi-exclamation-triangle text-warning"></i> 批量删除确认
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>警告：</strong>此操作将删除选中的文章，删除后的文章将移入回收站。
                </div>
                <p>您确定要删除选中的 <span id="batchDeleteSelectedCount" class="fw-bold text-danger">0</span> 篇文章吗？</p>
                <div class="mt-3">
                    <h6>选中的文章：</h6>
                    <div id="selectedArticlesList" class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                        <!-- 选中的文章列表将在这里显示 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="executeBatchDelete()">
                    <i class="bi bi-trash"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
const clientDetailId = {{ client.id }};
let clientDetailCurrentPage = 1;
let clientDetailCurrentFilters = {};
let clientDetailSelectedItems = new Set();

// 加载客户统计数据
function loadClientStats() {
    fetch(`/simple/api/clients/${clientDetailId}/stats`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatsCards(data.stats);
            } else {
                console.error('加载统计数据失败:', data.message);
            }
        })
        .catch(error => {
            console.error('加载统计数据出错:', error);
        });
}

// 更新统计卡片
function updateStatsCards(stats) {
    document.getElementById('totalCount').textContent = stats.total || 0;
    document.getElementById('pendingReviewCount').textContent = stats.pending_review || 0;
    document.getElementById('pendingImageCount').textContent = stats.pending_image || 0;
    document.getElementById('finalReviewCount').textContent = stats.final_review || 0;
    document.getElementById('clientPendingCount').textContent = stats.client_pending || 0;
    document.getElementById('pendingPublishCount').textContent = stats.pending_publish || 0;
    document.getElementById('publishedCount').textContent = stats.published || 0;
    document.getElementById('failedCount').textContent = stats.publish_failed || 0;
}

// 加载任务列表
function loadTasks() {
    fetch(`/simple/api/clients/${clientDetailId}/tasks`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTaskSelect(data.tasks);
            }
        })
        .catch(error => {
            console.error('加载任务列表出错:', error);
        });
}

// 更新任务选择器
function updateTaskSelect(tasks) {
    const taskSelect = document.getElementById('taskFilter');
    taskSelect.innerHTML = '<option value="">全部任务</option>';

    tasks.forEach(task => {
        const option = document.createElement('option');
        option.value = task.id;
        option.textContent = task.name;
        taskSelect.appendChild(option);
    });
}

// 加载批次列表（基于选择的任务）
function loadBatches(taskId = '') {
    const url = taskId ?
        `/simple/api/tasks/${taskId}/batches` :
        `/simple/api/clients/${clientDetailId}/batches`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateBatchSelect(data.batches);
            }
        })
        .catch(error => {
            console.error('加载批次列表出错:', error);
        });
}

// 更新批次选择器
function updateBatchSelect(batches) {
    const batchSelect = document.getElementById('batchFilter');
    batchSelect.innerHTML = '<option value="">全部批次</option>';

    batches.forEach(batch => {
        const option = document.createElement('option');
        option.value = batch.id;
        option.textContent = batch.name;
        batchSelect.appendChild(option);
    });
}

// 加载内容列表
function loadContents(page = 1) {
    clientDetailCurrentPage = page;

    // 构建查询参数
    const params = new URLSearchParams({
        page: page,
        ...clientDetailCurrentFilters
    });

    fetch(`/simple/api/clients/${clientDetailId}/contents?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateContentTable(data.contents);
                updatePagination(data.pagination);
                updateResultCount(data.pagination.total);
            } else {
                console.error('加载内容列表失败:', data.message);
            }
        })
        .catch(error => {
            console.error('加载内容列表出错:', error);
        });
}

// 更新内容表格
function updateContentTable(contents) {
    const tbody = document.getElementById('contentTableBody');

    if (contents.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted">
                    <i class="bi bi-inbox"></i>
                    <p class="mt-2">暂无数据</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = contents.map(content => `
        <tr>
            <td>
                <input type="checkbox" class="content-checkbox" value="${content.id}"
                       onchange="updateSelectedItems()">
            </td>
            <td><span class="badge bg-secondary">${content.task_id}</span></td>
            <td>${content.task_name}</td>
            <td>${content.batch_name}</td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${content.title}">
                    ${content.title}
                </div>
            </td>
            <td>
                <span class="badge status-badge status-${content.status}">
                    ${getStatusText(content.status)}
                </span>
            </td>
            <td>${formatDateTime(content.created_at)}</td>
            <td>${formatDateTime(content.reviewed_at)}</td>
            <td>${formatDateTime(content.published_at)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary btn-sm"
                            onclick="viewContent(${content.id})" title="查看详情">
                        <i class="bi bi-eye"></i> 查看
                    </button>
                    <button class="btn btn-outline-danger btn-sm"
                            onclick="deleteContent(${content.id})" title="删除文章">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('客户详情页面加载完成，客户ID:', clientDetailId);

    // 加载初始数据
    loadClientStats();
    loadTasks();
    loadContents();

    // 绑定筛选事件
    bindFilterEvents();

    // 标签页切换事件
    document.getElementById('tasks-tab').addEventListener('shown.bs.tab', function() {
        console.log('🔄 切换到任务管理标签页');
        loadTasksList();
    });

    // 回收站标签页切换事件
    const recycleTab = document.getElementById('recycle-tab');
    if (recycleTab) {
        recycleTab.addEventListener('shown.bs.tab', function() {
            console.log('🗑️ 切换到回收站标签页');
            loadRecycleBin(1);
        });
    }
});

// 绑定筛选事件
function bindFilterEvents() {
    // 任务选择变化时更新批次
    document.getElementById('taskFilter').addEventListener('change', function() {
        loadBatches(this.value);
    });
}

// 应用筛选
function applyFilters() {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);

    clientDetailCurrentFilters = {};
    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            clientDetailCurrentFilters[key] = value.trim();
        }
    }

    console.log('应用筛选条件:', clientDetailCurrentFilters);
    loadContents(1); // 重新加载第一页
    loadClientStats(); // 重新加载统计数据
}

// 重置筛选
function resetFilters() {
    document.getElementById('filterForm').reset();
    clientDetailCurrentFilters = {};
    loadContents(1);
    loadClientStats();
    loadBatches(); // 重新加载所有批次
}

// 更新分页
function updatePagination(pagination) {
    const nav = document.getElementById('paginationNav');
    const ul = document.getElementById('pagination');

    if (pagination.pages <= 1) {
        nav.style.display = 'none';
        return;
    }

    nav.style.display = 'block';
    ul.innerHTML = '';

    // 上一页
    if (pagination.has_prev) {
        ul.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadContents(${pagination.prev_num})">上一页</a>
            </li>
        `;
    }

    // 页码
    for (let i = 1; i <= pagination.pages; i++) {
        const active = i === pagination.page ? 'active' : '';
        ul.innerHTML += `
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="loadContents(${i})">${i}</a>
            </li>
        `;
    }

    // 下一页
    if (pagination.has_next) {
        ul.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadContents(${pagination.next_num})">下一页</a>
            </li>
        `;
    }
}

// 更新结果计数
function updateResultCount(total) {
    document.getElementById('resultCount').textContent = `共 ${total} 条记录`;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'draft': '待初审',
        'pending_review': '待初审',
        'first_reviewed': '图片待提交',
        'pending_image': '图片待提交',
        'image_uploaded': '图片待提交',
        'final_review': '最终审核',
        'pending_client_review': '客户待审核',
        'pending_publish': '待发布',
        'ready_to_publish': '待发布',
        'published': '已发布',
        'publish_failed': '发布失败'
    };
    return statusMap[status] || status;
}

// 格式化日期时间
function formatDateTime(datetime) {
    if (!datetime) return '-';
    const date = new Date(datetime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 选择所有项目
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.content-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelectedItems();
}

// 更新选中项目
function updateSelectedItems() {
    clientDetailSelectedItems.clear();
    document.querySelectorAll('.content-checkbox:checked').forEach(checkbox => {
        clientDetailSelectedItems.add(parseInt(checkbox.value));
    });

    // 更新批量操作按钮状态
    const hasSelected = clientDetailSelectedItems.size > 0;
    document.getElementById('selectedCount').textContent = clientDetailSelectedItems.size;

    // 更新批量删除按钮状态
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    if (batchDeleteBtn) {
        batchDeleteBtn.disabled = !hasSelected;
        if (hasSelected) {
            batchDeleteBtn.innerHTML = `<i class="bi bi-trash"></i> 批量删除 (${clientDetailSelectedItems.size})`;
        } else {
            batchDeleteBtn.innerHTML = '<i class="bi bi-trash"></i> 批量删除';
        }
    }

    // 如果有选中项，显示批量操作按钮
    if (hasSelected && !document.getElementById('batchActionBtn')) {
        const toolbar = document.querySelector('.card-header .d-flex');
        const batchBtn = document.createElement('button');
        batchBtn.id = 'batchActionBtn';
        batchBtn.className = 'btn btn-warning btn-sm me-2';
        batchBtn.innerHTML = '<i class="bi bi-gear"></i> 批量操作';
        batchBtn.setAttribute('data-bs-toggle', 'modal');
        batchBtn.setAttribute('data-bs-target', '#batchActionModal');
        toolbar.appendChild(batchBtn);
    } else if (!hasSelected && document.getElementById('batchActionBtn')) {
        document.getElementById('batchActionBtn').remove();
    }
}

// 显示批量删除模态框
function showBatchDeleteModal() {
    if (clientDetailSelectedItems.size === 0) {
        showToast('请先选择要删除的文章', 'warning');
        return;
    }

    // 更新选中数量
    document.getElementById('batchDeleteSelectedCount').textContent = clientDetailSelectedItems.size;

    // 获取选中文章的信息并显示
    const selectedArticlesList = document.getElementById('selectedArticlesList');
    selectedArticlesList.innerHTML = '';

    clientDetailSelectedItems.forEach(contentId => {
        const row = document.querySelector(`input[value="${contentId}"]`).closest('tr');
        const title = row.querySelector('td:nth-child(3)').textContent.trim();
        const status = row.querySelector('td:nth-child(4) .badge').textContent.trim();

        const listItem = document.createElement('div');
        listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
        listItem.innerHTML = `
            <div>
                <strong>${title}</strong>
                <br>
                <small class="text-muted">状态: ${status}</small>
            </div>
            <span class="badge bg-secondary">#${contentId}</span>
        `;
        selectedArticlesList.appendChild(listItem);
    });

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
    modal.show();
}

// 执行批量删除
function executeBatchDelete() {
    if (clientDetailSelectedItems.size === 0) {
        showToast('没有选中的文章', 'warning');
        return;
    }

    const contentIds = Array.from(clientDetailSelectedItems);

    fetch('/simple/api/batch-delete-contents', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            content_ids: contentIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`成功删除 ${data.deleted_count} 篇文章`, 'success');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal'));
            modal.hide();

            // 清空选中项
            clientDetailSelectedItems.clear();

            // 重新加载内容列表
            loadContents(clientDetailCurrentPage);

            // 更新按钮状态
            updateSelectedItems();
        } else {
            showToast('批量删除失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('批量删除失败:', error);
        showToast('批量删除失败，请重试', 'danger');
    });
}

// 查看内容详情
function viewContent(contentId) {
    console.log('查看内容:', contentId);

    // 显示加载状态
    const modal = new bootstrap.Modal(document.getElementById('contentViewModal'));
    const modalBody = document.getElementById('contentViewBody');

    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载文章内容...</p>
        </div>
    `;

    modal.show();

    // 获取文章详情 - 使用不需要权限验证的API
    fetch(`/simple/api/contents/${contentId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                renderContentView(data);
            } else {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        加载失败: ${data.message || '未知错误'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载文章详情失败:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    网络错误，请稍后重试: ${error.message}
                </div>
            `;
        });
}

// 渲染文章内容查看界面
function renderContentView(data) {
    const modalBody = document.getElementById('contentViewBody');
    const content = data.content;

    // 格式化状态显示
    const statusMap = {
        'draft': { text: '草稿', class: 'secondary' },
        'pending_review': { text: '待初审', class: 'warning' },
        'first_reviewed': { text: '初审通过', class: 'info' },
        'pending_image': { text: '图片待提交', class: 'info' },
        'image_uploaded': { text: '图片已上传', class: 'info' },
        'pending_final_review': { text: '待最终审核', class: 'warning' },
        'final_review': { text: '最终审核', class: 'warning' },
        'pending_client_review': { text: '客户待审核', class: 'warning' },
        'client_approved': { text: '客户已通过', class: 'success' },
        'client_rejected': { text: '客户已拒绝', class: 'danger' },
        'ready_to_publish': { text: '待发布', class: 'info' },
        'pending_publish': { text: '待发布', class: 'info' },
        'publishing': { text: '发布中', class: 'primary' },
        'published': { text: '已发布', class: 'success' },
        'publish_failed': { text: '发布失败', class: 'danger' },
        'publish_timeout': { text: '发布超时', class: 'warning' }
    };

    const status = statusMap[content.workflow_status] || { text: content.workflow_status, class: 'secondary' };

    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-file-text"></i> ${content.title || '无标题'}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>文章内容：</strong>
                            <div class="border rounded p-3 mt-2" style="background-color: #f8f9fa; white-space: pre-wrap;">${content.content || '暂无内容'}</div>
                        </div>

                        ${content.topics && content.topics.length > 0 ? `
                        <div class="mb-3">
                            <strong>话题标签：</strong>
                            <div class="mt-2">
                                ${content.topics.map(topic =>
                                    `<span class="badge bg-primary me-1">#${topic}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${content.at_users && content.at_users.length > 0 ? `
                        <div class="mb-3">
                            <strong>@用户：</strong>
                            <div class="mt-2">
                                ${content.at_users.map(user =>
                                    `<span class="badge bg-info me-1">${user}</span>`
                                ).join('')}
                            </div>
                        </div>
                        ` : ''}

                        ${content.location ? `
                        <div class="mb-3">
                            <strong>位置：</strong>
                            <span class="badge bg-warning">${content.location}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle"></i> 文章信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <strong>客户：</strong>
                            <span class="text-muted">${content.client_name || '未知'}</span>
                        </div>
                        <div class="mb-2">
                            <strong>状态：</strong>
                            <span class="badge bg-${status.class}">${status.text}</span>
                        </div>
                        <div class="mb-2">
                            <strong>创建时间：</strong><br>
                            <small class="text-muted">${content.created_at}</small>
                        </div>
                        ${content.internal_review_status ? `
                        <div class="mb-2">
                            <strong>内部审核：</strong>
                            <span class="badge bg-${content.internal_review_status === 'approved' ? 'success' : 'warning'}">${content.internal_review_status === 'approved' ? '已通过' : '待审核'}</span>
                        </div>
                        ` : ''}
                        ${content.client_review_status ? `
                        <div class="mb-2">
                            <strong>客户审核：</strong>
                            <span class="badge bg-${content.client_review_status === 'approved' ? 'success' : 'warning'}">${content.client_review_status === 'approved' ? '已通过' : '待审核'}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                ${content.publish_records && content.publish_records.length > 0 ? `
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-broadcast"></i> 发布记录
                        </h6>
                    </div>
                    <div class="card-body">
                        ${content.publish_records.map(record => `
                            <div class="mb-3 p-2 border rounded">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-1">
                                            <strong>平台：</strong>
                                            <span class="badge bg-primary">${record.platform || '未知平台'}</span>
                                        </div>
                                        ${record.account ? `
                                        <div class="mb-1">
                                            <strong>账号：</strong>
                                            <span class="text-muted">${record.account}</span>
                                        </div>
                                        ` : ''}
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-1">
                                            <strong>状态：</strong>
                                            <span class="badge bg-${record.status === 'success' ? 'success' : record.status === 'failed' ? 'danger' : 'warning'}">${record.status === 'success' ? '发布成功' : record.status === 'failed' ? '发布失败' : '待发布'}</span>
                                        </div>
                                        ${record.publish_time ? `
                                        <div class="mb-1">
                                            <strong>时间：</strong>
                                            <small class="text-muted">${record.publish_time}</small>
                                        </div>
                                        ` : ''}
                                    </div>
                                </div>
                                ${record.publish_url ? `
                                <div class="mt-2">
                                    <strong>链接：</strong>
                                    <button type="button"
                                            class="btn btn-primary btn-sm ms-1"
                                            onclick="window.open('${record.publish_url}', '_blank')"
                                            oncontextmenu="copyPublishUrl(event, '${record.publish_url}')"
                                            title="左键打开发布链接，右键复制链接">
                                        <i class="bi bi-link-45deg"></i> 发布链接
                                    </button>
                                </div>
                                ` : ''}
                                ${record.error_message ? `
                                <div class="mt-2">
                                    <strong>消息：</strong>
                                    <small class="text-muted">${record.error_message}</small>
                                </div>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}

                <div class="card mt-3" id="contentImagesCard" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-images"></i> 配图
                        </h6>
                    </div>
                    <div class="card-body" id="contentImagesBody">
                        <!-- 图片将通过AJAX加载 -->
                    </div>
                </div>
            </div>
        </div>
    `;

    // 异步加载图片
    loadContentImages(content.id);
}

// 复制发布链接
function copyPublishUrl(event, url) {
    event.preventDefault(); // 阻止右键菜单

    // 使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(url).then(() => {
            showToast('链接已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(url);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(url);
    }
}

// 降级复制方案
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // 避免滚动到底部
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('链接已复制到剪贴板', 'success');
        } else {
            showToast('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('降级复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    }

    document.body.removeChild(textArea);
}

// 加载文章图片
function loadContentImages(contentId) {
    console.log('开始加载图片，contentId:', contentId);

    fetch(`/simple/api/images/${contentId}`)
        .then(response => {
            console.log('图片API响应状态:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('图片API响应数据:', data);

            if (data.success && data.images && data.images.length > 0) {
                const imagesCard = document.getElementById('contentImagesCard');
                const imagesBody = document.getElementById('contentImagesBody');

                console.log('找到图片数量:', data.images.length);

                imagesCard.style.display = 'block';

                imagesBody.innerHTML = `
                    <div class="row">
                        ${data.images.map((image, index) => {
                            console.log(`图片 ${index + 1}:`, image);
                            const imgSrc = image.thumbnail_url || image.image_url;
                            const fullImgSrc = image.image_url;
                            console.log(`图片 ${index + 1} 缩略图URL:`, imgSrc);
                            console.log(`图片 ${index + 1} 完整URL:`, fullImgSrc);

                            return `
                                <div class="col-6 mb-2">
                                    <img src="${imgSrc}"
                                         class="img-fluid rounded"
                                         style="cursor: pointer; max-height: 100px; object-fit: cover; width: 100%;"
                                         onclick="showImageModal('${fullImgSrc}')"
                                         title="点击查看大图"
                                         onerror="console.error('图片加载失败:', this.src)">
                                </div>
                            `;
                        }).join('')}
                    </div>
                `;
            } else {
                console.log('没有找到图片或图片数组为空');
            }
        })
        .catch(error => {
            console.error('加载图片失败:', error);
        });
}

// 删除文章内容
function deleteContent(contentId) {
    if (!confirm('确定要删除这篇文章吗？此操作不可恢复！')) {
        return;
    }

    console.log('删除文章:', contentId);

    // 防止重复点击
    const deleteBtn = event.target.closest('button');
    if (deleteBtn) {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm"></i> 删除中...';
    }

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch(`/simple/api/contents/${contentId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            ...(csrfToken && { 'X-CSRFToken': csrfToken })
        }
    })
    .then(response => {
        console.log('删除响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('删除响应数据:', data);
        if (data.success) {
            if (typeof showToast === 'function') {
                showToast('文章删除成功', 'success');
            }
            // 重新加载文章列表
            loadContents(clientDetailCurrentPage);
            // 重新加载统计数据
            loadClientStats();
        } else {
            if (typeof showToast === 'function') {
                showToast('删除失败：' + (data.message || '未知错误'), 'error');
            }
        }
    })
    .catch(error => {
        console.error('删除文章失败:', error);
        if (typeof showToast === 'function') {
            showToast('删除失败，请重试', 'error');
        }
    })
    .finally(() => {
        // 恢复按钮状态
        if (deleteBtn) {
            deleteBtn.disabled = false;
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 删除';
        }
    });
}

// 显示图片大图
function showImageModal(imageUrl) {
    console.log('显示图片大图:', imageUrl);

    if (!imageUrl) {
        console.error('图片URL为空');
        return;
    }

    // 创建图片模态框
    const imageModal = document.createElement('div');
    imageModal.className = 'modal fade';
    imageModal.id = 'imagePreviewModal_' + Date.now(); // 添加唯一ID避免冲突
    imageModal.innerHTML = `
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-image"></i> 图片预览
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="${imageUrl}"
                         class="img-fluid"
                         style="max-height: 70vh; max-width: 100%;"
                         onerror="this.style.display='none'; this.parentElement.innerHTML='<div class=\\'text-danger\\'>图片加载失败</div>'; console.error('图片加载失败:', this.src);">
                </div>
                <div class="modal-footer">
                    <a href="${imageUrl}" target="_blank" class="btn btn-primary">
                        <i class="bi bi-box-arrow-up-right"></i> 在新窗口打开
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(imageModal);
    const modal = new bootstrap.Modal(imageModal);
    modal.show();

    // 模态框关闭后移除元素
    imageModal.addEventListener('hidden.bs.modal', function() {
        try {
            document.body.removeChild(imageModal);
        } catch (e) {
            console.warn('移除图片模态框失败:', e);
        }
    });
}



// 执行批量操作
function executeBatchAction() {
    const status = document.getElementById('batchStatus').value;
    if (!status) {
        alert('请选择要更新的状态');
        return;
    }

    if (clientDetailSelectedItems.size === 0) {
        alert('请选择要操作的内容');
        return;
    }

    const contentIds = Array.from(clientDetailSelectedItems);

    fetch('/simple/api/contents/batch-update-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content_ids: contentIds,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`成功更新 ${data.updated_count} 条内容的状态`);
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('batchActionModal')).hide();
            // 重新加载数据
            loadContents(clientDetailCurrentPage);
            loadClientStats();
            // 清空选择
            clientDetailSelectedItems.clear();
            document.getElementById('selectAll').checked = false;
            updateSelectedItems();
        } else {
            alert('批量更新失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('批量更新出错:', error);
        alert('批量更新失败');
    });
}

// 导出数据
function exportData() {
    const params = new URLSearchParams(clientDetailCurrentFilters);
    window.open(`/simple/api/clients/${clientDetailId}/contents/export?${params}`, '_blank');
}

// ==================== 任务管理功能 ====================

let selectedTasks = new Set();
let currentEditingTaskId = null;

// 加载任务列表
function loadTasksList() {
    console.log('🔄 加载任务列表');

    fetch(`/simple/api/clients/${clientDetailId}/tasks-management`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Response is not JSON');
            }
        })
        .then(data => {
            if (data.success) {
                renderTasksList(data.tasks);
            } else {
                console.error('加载任务列表失败:', data.message);
                document.getElementById('tasksTableBody').innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            加载失败: ${data.message}
                        </td>
                    </tr>
                `;
            }
        })
        .catch(error => {
            console.error('加载任务列表出错:', error);
            document.getElementById('tasksTableBody').innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-danger">
                        <i class="bi bi-wifi-off"></i>
                        网络错误，请稍后重试
                    </td>
                </tr>
            `;
        });
}

// 渲染任务列表
function renderTasksList(tasks) {
    const tbody = document.getElementById('tasksTableBody');

    if (tasks.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="bi bi-inbox"></i>
                    <p class="mt-2">暂无任务数据</p>
                    <button type="button" class="btn btn-primary btn-sm" onclick="createNewTask()">
                        <i class="bi bi-plus"></i> 创建第一个任务
                    </button>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = tasks.map(task => `
        <tr>
            <td>
                <input type="checkbox" class="task-checkbox" value="${task.id}" onchange="updateSelectedTasks()">
            </td>
            <td>
                <span class="badge bg-secondary">${task.id}</span>
            </td>
            <td>
                <strong>${task.name}</strong>
            </td>
            <td>
                <span class="text-muted">${task.description || '-'}</span>
            </td>
            <td>
                <span class="badge bg-${getTaskStatusColor(task.status)}">${getTaskStatusText(task.status)}</span>
            </td>
            <td>
                <span class="badge bg-info">${task.actual_count || 0}</span>
            </td>
            <td>
                <small class="text-muted">${formatDateTime(task.created_at)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="editTask(${task.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteTask(${task.id}, '${task.name}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// 获取任务状态颜色
function getTaskStatusColor(status) {
    const colors = {
        'processing': 'primary',
        'completed': 'success',
        'paused': 'warning'
    };
    return colors[status] || 'secondary';
}

// 获取任务状态文本
function getTaskStatusText(status) {
    const texts = {
        'processing': '进行中',
        'completed': '已完成',
        'paused': '已暂停'
    };
    return texts[status] || status;
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 更新选中的任务
function updateSelectedTasks() {
    selectedTasks.clear();
    document.querySelectorAll('.task-checkbox:checked').forEach(checkbox => {
        selectedTasks.add(parseInt(checkbox.value));
    });

    // 更新批量删除按钮显示状态
    const batchDeleteBtn = document.getElementById('batchDeleteTasksBtn');
    if (selectedTasks.size > 0) {
        batchDeleteBtn.style.display = 'inline-block';
    } else {
        batchDeleteBtn.style.display = 'none';
    }

    // 更新全选复选框状态
    const selectAllTasks = document.getElementById('selectAllTasks');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    selectAllTasks.checked = taskCheckboxes.length > 0 && selectedTasks.size === taskCheckboxes.length;
}

// 全选/取消全选任务
function toggleSelectAllTasks() {
    const selectAll = document.getElementById('selectAllTasks');
    const checkboxes = document.querySelectorAll('.task-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelectedTasks();
}

// 创建新任务
function createNewTask() {
    console.log('🔄 创建新任务');
    currentEditingTaskId = null;

    // 重置表单
    document.getElementById('taskForm').reset();
    document.getElementById('taskModalTitle').textContent = '新建任务';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
    modal.show();
}

// 编辑任务
function editTask(taskId) {
    console.log('🔄 编辑任务:', taskId);
    currentEditingTaskId = taskId;

    // 获取任务详情
    fetch(`/simple/api/tasks/${taskId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Response is not JSON');
            }
        })
        .then(data => {
            if (data.success) {
                const task = data.task;

                // 填充表单
                document.getElementById('taskName').value = task.name;
                document.getElementById('taskDescription').value = task.description || '';
                document.getElementById('taskStatus').value = task.status;

                // 更新标题
                document.getElementById('taskModalTitle').textContent = '编辑任务';

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('taskModal'));
                modal.show();
            } else {
                showToast('获取任务详情失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取任务详情出错:', error);
            showToast('获取任务详情失败，请重试', 'error');
        });
}

// 保存任务
function saveTask() {
    const form = document.getElementById('taskForm');
    const formData = new FormData(form);

    // 验证表单
    if (!formData.get('name').trim()) {
        showToast('请输入任务名称', 'warning');
        return;
    }

    // 添加客户ID
    formData.append('client_id', clientDetailId);

    const url = currentEditingTaskId ?
        `/simple/api/tasks/${currentEditingTaskId}` :
        '/simple/api/tasks';

    const method = currentEditingTaskId ? 'PUT' : 'POST';

    // 添加CSRF token到表单数据
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
        formData.append('csrf_token', csrfToken);
    }

    fetch(url, {
        method: method,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken || ''
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else {
            throw new Error('Response is not JSON');
        }
    })
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('taskModal'));
            modal.hide();

            // 重新加载任务列表
            loadTasksList();

            // 显示成功提示
            showToast(currentEditingTaskId ? '任务更新成功' : '任务创建成功', 'success');
        } else {
            showToast('保存失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存任务出错:', error);
        showToast('保存失败，请重试', 'error');
    });
}

// 删除单个任务
function deleteTask(taskId, taskName) {
    console.log('🔄 准备删除任务:', taskId, taskName);

    // 获取任务统计信息
    fetch(`/simple/api/tasks/${taskId}/stats`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Response is not JSON');
            }
        })
        .then(data => {
            if (data.success) {
                const stats = data.stats;

                // 更新删除确认模态框
                document.getElementById('deleteTaskName').textContent = taskName;
                document.getElementById('taskDeleteStats').innerHTML = `
                    <strong>该任务包含：</strong><br>
                    • ${stats.total_contents || 0} 篇文章<br>
                    • ${stats.total_images || 0} 个图片文件<br>
                    • ${stats.total_batches || 0} 个批次
                `;

                // 设置当前要删除的任务ID
                window.currentDeletingTaskId = taskId;

                // 显示删除确认模态框
                const modal = new bootstrap.Modal(document.getElementById('deleteTaskModal'));
                modal.show();
            } else {
                // 如果获取统计失败，直接显示基本确认框
                if (confirm(`确定要删除任务"${taskName}"吗？\n\n警告：此操作将删除该任务下的所有文章和图片，且不可恢复！`)) {
                    executeDeleteTask(taskId);
                }
            }
        })
        .catch(error => {
            console.error('获取任务统计出错:', error);
            // 如果获取统计失败，直接显示基本确认框
            if (confirm(`确定要删除任务"${taskName}"吗？\n\n警告：此操作将删除该任务下的所有文章和图片，且不可恢复！`)) {
                executeDeleteTask(taskId);
            }
        });
}

// 确认删除任务
function confirmDeleteTask() {
    if (window.currentDeletingTaskId) {
        executeDeleteTask(window.currentDeletingTaskId);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteTaskModal'));
        modal.hide();
    }
}

// 执行删除任务
function executeDeleteTask(taskId) {
    console.log('🗑️ 执行删除任务:', taskId);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch(`/simple/api/tasks/${taskId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken || ''
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else {
            throw new Error('Response is not JSON');
        }
    })
    .then(data => {
        if (data.success) {
            const stats = data.deleted_stats;
            const message = `任务删除成功！删除了 ${stats.contents || 0} 篇文章、${stats.images || 0} 个图片、${stats.batches || 0} 个批次`;
            showToast(message, 'success');

            // 重新加载任务列表
            loadTasksList();

            // 重新加载统计数据
            loadClientStats();
        } else {
            showToast('删除失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除任务出错:', error);
        showToast('删除失败，请重试', 'error');
    });
}

// 批量删除任务
function batchDeleteTasks() {
    if (selectedTasks.size === 0) {
        alert('请先选择要删除的任务');
        return;
    }

    const taskIds = Array.from(selectedTasks);
    const confirmMessage = `确定要删除选中的 ${taskIds.length} 个任务吗？\n\n警告：此操作将删除这些任务下的所有文章和图片，且不可恢复！`;

    if (confirm(confirmMessage)) {
        console.log('🗑️ 批量删除任务:', taskIds);

        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        fetch('/simple/api/tasks/batch-delete', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken || ''
            },
            body: JSON.stringify({
                task_ids: taskIds
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                throw new Error('Response is not JSON');
            }
        })
        .then(data => {
            if (data.success) {
                const stats = data.deleted_stats;
                const message = `批量删除成功！删除了 ${stats.tasks || 0} 个任务、${stats.contents || 0} 篇文章、${stats.images || 0} 个图片、${stats.batches || 0} 个批次`;
                showToast(message, 'success');

                // 清空选择
                selectedTasks.clear();

                // 重新加载任务列表
                loadTasksList();

                // 重新加载统计数据
                loadClientStats();
            } else {
                showToast('批量删除失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('批量删除任务出错:', error);
            showToast('批量删除失败，请重试', 'error');
        });
    }
}

// ==================== 回收站功能 ====================

let selectedRecycleItems = new Set();
let recycleCurrentPage = 1;

// 加载回收站数据
function loadRecycleBin(page = 1) {
    console.log('加载回收站数据，页码:', page);

    recycleCurrentPage = page;

    fetch(`/simple/api/clients/${clientDetailId}/recycle?page=${page}`)
        .then(response => response.json())
        .then(data => {
            console.log('回收站数据:', data);

            if (data.success) {
                updateRecycleTable(data.contents);
                updateRecyclePagination(data.pagination);
                updateRecycleCount(data.pagination.total);
            } else {
                console.error('加载回收站数据失败:', data.message);
                showToast('加载回收站数据失败', 'error');
            }
        })
        .catch(error => {
            console.error('加载回收站数据失败:', error);
            showToast('加载回收站数据失败，请重试', 'error');
        });
}

// 更新回收站表格
function updateRecycleTable(contents) {
    const tbody = document.getElementById('recycleTableBody');

    if (!contents || contents.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="bi bi-trash3 fs-1"></i>
                    <p class="mt-2">回收站为空</p>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = contents.map(content => `
        <tr>
            <td>
                <input type="checkbox" value="${content.id}" onchange="toggleRecycleSelection(${content.id})">
            </td>
            <td><span class="badge bg-secondary">${content.task_id}</span></td>
            <td>${content.task_name || '-'}</td>
            <td>${content.batch_name || '-'}</td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${content.title}">
                    ${content.title}
                </div>
            </td>
            <td>
                <small class="text-muted">
                    ${content.deleted_at ? new Date(content.deleted_at).toLocaleString() : '-'}
                </small>
            </td>
            <td>
                <button class="btn btn-outline-info btn-sm me-1" onclick="previewRecycleContent(${content.id})" title="预览">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-outline-success btn-sm me-1" onclick="restoreContent(${content.id})" title="恢复">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="permanentDeleteContent(${content.id})" title="永久删除">
                    <i class="bi bi-trash-fill"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// 更新回收站分页
function updateRecyclePagination(pagination) {
    const nav = document.getElementById('recyclePaginationNav');
    const ul = document.getElementById('recyclePagination');

    if (pagination.pages <= 1) {
        nav.style.display = 'none';
        return;
    }

    nav.style.display = 'block';

    let paginationHTML = '';

    // 上一页
    if (pagination.has_prev) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadRecycleBin(${pagination.prev_num})">上一页</a></li>`;
    }

    // 页码
    for (let i = 1; i <= pagination.pages; i++) {
        const active = i === pagination.page ? 'active' : '';
        paginationHTML += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadRecycleBin(${i})">${i}</a></li>`;
    }

    // 下一页
    if (pagination.has_next) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadRecycleBin(${pagination.next_num})">下一页</a></li>`;
    }

    ul.innerHTML = paginationHTML;
}

// 更新回收站数量
function updateRecycleCount(count) {
    document.getElementById('recycleCount').textContent = count || 0;
}

// 预览回收站中的文章
function previewRecycleContent(contentId) {
    console.log('预览回收站文章:', contentId);

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'recyclePreviewModal_' + Date.now();
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-eye text-info"></i> 文章预览
                        <span class="badge bg-danger ms-2">已删除</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="recyclePreviewContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载文章内容...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // 加载文章内容
    fetch(`/simple/api/contents/${contentId}/view`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(html => {
            document.getElementById('recyclePreviewContent').innerHTML = html;
        })
        .catch(error => {
            console.error('加载文章预览失败:', error);
            document.getElementById('recyclePreviewContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    加载文章内容失败：${error.message}
                </div>
            `;
        });

    // 模态框关闭后移除元素
    modal.addEventListener('hidden.bs.modal', function() {
        try {
            document.body.removeChild(modal);
        } catch (e) {
            console.warn('移除预览模态框失败:', e);
        }
    });
}

// 从预览模态框恢复文章
function restoreContentFromPreview(contentId, modalId) {
    // 关闭预览模态框
    const modal = document.getElementById(modalId);
    if (modal) {
        const bootstrapModal = bootstrap.Modal.getInstance(modal);
        if (bootstrapModal) {
            bootstrapModal.hide();
        }
    }

    // 执行恢复操作
    restoreContent(contentId);
}

// 从预览模态框永久删除文章
function permanentDeleteContentFromPreview(contentId, modalId) {
    // 关闭预览模态框
    const modal = document.getElementById(modalId);
    if (modal) {
        const bootstrapModal = bootstrap.Modal.getInstance(modal);
        if (bootstrapModal) {
            bootstrapModal.hide();
        }
    }

    // 执行永久删除操作
    permanentDeleteContent(contentId);
}

// 切换回收站项目选择
function toggleRecycleSelection(contentId) {
    if (selectedRecycleItems.has(contentId)) {
        selectedRecycleItems.delete(contentId);
    } else {
        selectedRecycleItems.add(contentId);
    }

    updateRecycleBatchButtons();
}

// 全选/取消全选回收站项目
function toggleSelectAllRecycle() {
    const checkbox = document.getElementById('selectAllRecycle');
    const checkboxes = document.querySelectorAll('#recycleTableBody input[type="checkbox"]');

    selectedRecycleItems.clear();

    checkboxes.forEach(cb => {
        cb.checked = checkbox.checked;
        if (checkbox.checked) {
            selectedRecycleItems.add(parseInt(cb.value));
        }
    });

    updateRecycleBatchButtons();
}

// 更新批量操作按钮状态
function updateRecycleBatchButtons() {
    const restoreBtn = document.getElementById('batchRestoreBtn');
    const deleteBtn = document.getElementById('batchPermanentDeleteBtn');

    if (selectedRecycleItems.size > 0) {
        restoreBtn.style.display = 'inline-block';
        deleteBtn.style.display = 'inline-block';
    } else {
        restoreBtn.style.display = 'none';
        deleteBtn.style.display = 'none';
    }
}

// 恢复单个文章
function restoreContent(contentId) {
    if (!confirm('确定要恢复这篇文章吗？\n\n恢复后文章将重置为草稿状态，需要重新进入审核流程。')) {
        return;
    }

    console.log('恢复文章:', contentId);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch(`/simple/api/contents/${contentId}/restore`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            ...(csrfToken && { 'X-CSRFToken': csrfToken })
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('文章恢复成功', 'success');
            loadRecycleBin(recycleCurrentPage);
            loadClientStats(); // 更新统计数据
        } else {
            showToast('恢复失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('恢复文章失败:', error);
        showToast('恢复失败，请重试', 'error');
    });
}

// 永久删除单个文章
function permanentDeleteContent(contentId) {
    if (!confirm('确定要永久删除这篇文章吗？此操作不可恢复！')) {
        return;
    }

    console.log('永久删除文章:', contentId);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch(`/simple/api/contents/${contentId}/permanent-delete`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            ...(csrfToken && { 'X-CSRFToken': csrfToken })
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('文章已永久删除', 'success');
            loadRecycleBin(recycleCurrentPage);
        } else {
            showToast('删除失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('永久删除文章失败:', error);
        showToast('删除失败，请重试', 'error');
    });
}

// 批量恢复文章
function batchRestoreContents() {
    if (selectedRecycleItems.size === 0) {
        alert('请先选择要恢复的文章');
        return;
    }

    const contentIds = Array.from(selectedRecycleItems);
    if (!confirm(`确定要恢复选中的 ${contentIds.length} 篇文章吗？\n\n恢复后所有文章将重置为草稿状态，需要重新进入审核流程。`)) {
        return;
    }

    console.log('批量恢复文章:', contentIds);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch('/simple/api/contents/batch-restore', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            ...(csrfToken && { 'X-CSRFToken': csrfToken })
        },
        body: JSON.stringify({
            content_ids: contentIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`成功恢复 ${data.restored_count} 篇文章`, 'success');
            selectedRecycleItems.clear();
            loadRecycleBin(recycleCurrentPage);
            loadClientStats(); // 更新统计数据
        } else {
            showToast('批量恢复失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('批量恢复文章失败:', error);
        showToast('批量恢复失败，请重试', 'error');
    });
}

// 批量永久删除文章
function batchPermanentDelete() {
    if (selectedRecycleItems.size === 0) {
        alert('请先选择要永久删除的文章');
        return;
    }

    const contentIds = Array.from(selectedRecycleItems);
    if (!confirm(`确定要永久删除选中的 ${contentIds.length} 篇文章吗？此操作不可恢复！`)) {
        return;
    }

    console.log('批量永久删除文章:', contentIds);

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch('/simple/api/contents/batch-permanent-delete', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            ...(csrfToken && { 'X-CSRFToken': csrfToken })
        },
        body: JSON.stringify({
            content_ids: contentIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 后端返回的是 stats 对象，包含 contents 和 images 数量
            const deletedCount = data.stats ? data.stats.contents : (data.deleted_count || 0);
            showToast(`批量删除任务已启动，将永久删除 ${deletedCount} 篇文章`, 'success');
            selectedRecycleItems.clear();
            loadRecycleBin(recycleCurrentPage);
        } else {
            showToast('批量删除失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('批量永久删除文章失败:', error);
        showToast('批量删除失败，请重试', 'error');
    });
}

// 清空回收站
function clearRecycleBin() {
    if (!confirm('确定要清空整个回收站吗？此操作将永久删除所有已删除的文章，且不可恢复！')) {
        return;
    }

    console.log('清空回收站');

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch(`/simple/api/clients/${clientDetailId}/recycle/clear`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            ...(csrfToken && { 'X-CSRFToken': csrfToken })
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 后端返回的是 stats 对象，包含 contents 和 images 数量
            const deletedCount = data.stats ? data.stats.contents : (data.deleted_count || 0);
            showToast(`回收站清空任务已启动，将永久删除 ${deletedCount} 篇文章`, 'success');
            selectedRecycleItems.clear();
            loadRecycleBin(1);
        } else {
            showToast('清空回收站失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('清空回收站失败:', error);
        showToast('清空回收站失败，请重试', 'error');
    });
}


</script>
{% endblock %}
