<!-- 最终审核客户列表页面 -->
<style>
/* 排序按钮样式 */
.sort-btn {
    transition: all 0.2s ease;
    border-radius: 20px;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}
.sort-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.sort-btn.active {
    background-color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

/* 搜索框样式 */
.input-group-sm .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
}
.input-group-sm .input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}
#searchInput:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="bi bi-check-circle"></i> 最终审核</h2>
            <p class="text-muted">选择客户查看需要最终审核的文案</p>
        </div>
    </div>

    <!-- 客户列表 -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="card-title mb-0">客户列表</h6>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-2">
                        <!-- 排序说明 -->
                        <small class="text-muted me-1">排序:</small>

                        <!-- 排序按钮 -->
                        <div class="btn-group btn-group-sm" role="group" aria-label="排序选项">
                            <button type="button" class="btn btn-outline-primary sort-btn {{ 'active' if sort_by == 'desc' else '' }}"
                                    data-sort="desc" title="按待最终审核数量从多到少排序">
                                <i class="bi bi-sort-numeric-down"></i> 多→少
                            </button>
                            <button type="button" class="btn btn-outline-primary sort-btn {{ 'active' if sort_by == 'asc' else '' }}"
                                    data-sort="asc" title="按待最终审核数量从少到多排序">
                                <i class="bi bi-sort-numeric-up"></i> 少→多
                            </button>
                        </div>

                        <!-- 排序提示 -->
                        <small class="text-muted ms-1">
                            {% if sort_by == 'asc' %}
                                (按数量↑)
                            {% else %}
                                (按数量↓)
                            {% endif %}
                        </small>

                        <!-- 搜索框 -->
                        <form method="GET" action="{{ url_for('main_simple.final_review') }}" class="d-flex flex-grow-1" id="searchForm">
                            <input type="hidden" name="sort_by" id="sortByInput" value="{{ sort_by or '' }}">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" name="search" id="searchInput"
                                       placeholder="搜索客户名称..." value="{{ search or '' }}" autocomplete="off">
                                <button class="btn btn-outline-primary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                                {% if search %}
                                <a href="{{ url_for('main_simple.final_review') }}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x"></i>
                                </a>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if clients_with_pending %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>客户名称</th>
                                <th>待最终审核文案数量</th>
                                <th>最早操作时间</th>
                                <th>最新操作时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients_with_pending %}
                            <tr>
                                <td>
                                    <strong>{{ client.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-info text-white">
                                        {{ client.pending_count }} 篇待最终审核
                                    </span>
                                </td>
                                <td>
                                    {% if client.earliest_time %}
                                        <small class="text-muted">
                                            <i class="bi bi-clock"></i>
                                            {{ client.earliest_time.strftime('%m-%d %H:%M') }}
                                        </small>
                                    {% else %}
                                        <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if client.latest_time %}
                                        <small class="text-muted">
                                            <i class="bi bi-clock"></i>
                                            {{ client.latest_time.strftime('%m-%d %H:%M') }}
                                        </small>
                                    {% else %}
                                        <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('main_simple.final_review_client_detail', client_id=client.id) }}"
                                       class="btn btn-primary btn-sm">
                                        <i class="bi bi-check-circle"></i> 进入最终审核
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-check-circle text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无待最终审核的文案</h4>
                    <p class="text-muted">所有文案都已完成最终审核</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 绑定排序按钮事件
    document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');

            // 更新按钮状态
            document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // 更新隐藏字段
            document.getElementById('sortByInput').value = sortBy;

            // 提交表单
            document.getElementById('searchForm').submit();
        });
    });

    console.log('最终审核客户列表页面已加载');
});
</script>
