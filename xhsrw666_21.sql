-- MySQL dump 10.13  Distrib 5.7.26, for Win64 (x86_64)
--
-- Host: localhost    Database: xhsrw666
-- ------------------------------------------------------
-- Server version	5.7.26

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `alembic_version`
--

DROP TABLE IF EXISTS `alembic_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alembic_version` (
  `version_num` varchar(32) NOT NULL,
  PRIMARY KEY (`version_num`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alembic_version`
--

LOCK TABLES `alembic_version` WRITE;
/*!40000 ALTER TABLE `alembic_version` DISABLE KEYS */;
INSERT INTO `alembic_version` VALUES ('add_performance_indexes'),('f53868527c9d');
/*!40000 ALTER TABLE `alembic_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `batches`
--

DROP TABLE IF EXISTS `batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `content_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前批次中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `batches_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `batches_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batches`
--

LOCK TABLES `batches` WRITE;
/*!40000 ALTER TABLE `batches` DISABLE KEYS */;
INSERT INTO `batches` VALUES (54,35,'批次1',8,'2025-08-02 22:24:26',1,0),(55,36,'批次1',8,'2025-08-02 22:31:50',1,0);
/*!40000 ALTER TABLE `batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_deletion_logs`
--

DROP TABLE IF EXISTS `client_deletion_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_deletion_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `queue_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `log_level` enum('info','warning','error') DEFAULT 'info',
  `stage` varchar(50) NOT NULL COMMENT '删除阶段',
  `message` text NOT NULL COMMENT '日志消息',
  `details` json DEFAULT NULL COMMENT '详细信息，JSON格式',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_queue_id` (`queue_id`),
  KEY `idx_client_id` (`client_id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_stage` (`stage`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `client_deletion_logs_ibfk_1` FOREIGN KEY (`queue_id`) REFERENCES `client_deletion_queue` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='客户删除日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_deletion_logs`
--

LOCK TABLES `client_deletion_logs` WRITE;
/*!40000 ALTER TABLE `client_deletion_logs` DISABLE KEYS */;
INSERT INTO `client_deletion_logs` VALUES (1,10,1,'info','start','开始批量删除 2 篇文章',NULL,'2025-08-02 04:20:48'),(2,10,1,'info','deleting_content','删除文章 207',NULL,'2025-08-02 04:20:48'),(3,10,1,'info','deleting_content','删除文章 203',NULL,'2025-08-02 04:20:49'),(4,10,1,'info','completed','批量删除完成: 删除 2 篇文章, 0 张图片',NULL,'2025-08-02 04:20:50'),(5,11,1,'info','start','开始批量删除 1 篇文章',NULL,'2025-08-02 04:23:20'),(6,11,1,'info','deleting_content','删除文章 204',NULL,'2025-08-02 04:23:20'),(7,11,1,'info','completed','批量删除完成: 删除 1 篇文章, 0 张图片',NULL,'2025-08-02 04:23:21'),(8,12,1,'info','start','开始批量删除 1 篇文章',NULL,'2025-08-02 04:26:35'),(9,12,1,'info','deleting_content','删除文章 202',NULL,'2025-08-02 04:26:35'),(10,12,1,'info','completed','批量删除完成: 删除 1 篇文章, 0 张图片',NULL,'2025-08-02 04:26:37'),(11,13,1,'info','start','开始清空客户 1 的回收站',NULL,'2025-08-02 12:17:43'),(12,13,1,'info','deleting_content','删除文章 147',NULL,'2025-08-02 12:17:43'),(13,13,1,'info','deleting_content','删除文章 209',NULL,'2025-08-02 12:17:44'),(14,13,1,'info','deleting_content','删除文章 211',NULL,'2025-08-02 12:17:45'),(15,13,1,'info','deleting_content','删除文章 213',NULL,'2025-08-02 12:17:47'),(16,13,1,'info','completed','回收站清空完成: 删除 4 篇文章, 2 张图片',NULL,'2025-08-02 12:17:48');
/*!40000 ALTER TABLE `client_deletion_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_deletion_queue`
--

DROP TABLE IF EXISTS `client_deletion_queue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_deletion_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `status` enum('queued','running','completed','failed') DEFAULT 'queued' COMMENT '任务状态',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数字越大优先级越高',
  `total_images` int(11) DEFAULT '0' COMMENT '总图片数量',
  `deleted_images` int(11) DEFAULT '0' COMMENT '已删除图片数量',
  `total_contents` int(11) DEFAULT '0' COMMENT '总文案数量',
  `deleted_contents` int(11) DEFAULT '0' COMMENT '已删除文案数量',
  `estimated_duration` int(11) DEFAULT '0' COMMENT '预估耗时（秒）',
  `actual_duration` int(11) DEFAULT '0' COMMENT '实际耗时（秒）',
  `current_stage` varchar(50) DEFAULT 'pending' COMMENT '当前阶段：pending, deleting_images, deleting_contents, deleting_tasks, deleting_client, completed',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始执行时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `client_deletion_queue_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COMMENT='客户删除任务队列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_deletion_queue`
--

LOCK TABLES `client_deletion_queue` WRITE;
/*!40000 ALTER TABLE `client_deletion_queue` DISABLE KEYS */;
INSERT INTO `client_deletion_queue` VALUES (1,40,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-01 21:03:48',NULL,NULL,'2025-08-01 21:03:48'),(4,1,'queued',0,0,0,2,0,12,0,'batch_content_deletion','[203, 207]','2025-08-01 21:21:13',NULL,NULL,'2025-08-01 21:21:13'),(5,1,'queued',0,0,0,2,0,12,0,'batch_content_deletion','[207, 202]','2025-08-01 21:21:26',NULL,NULL,'2025-08-01 21:21:26'),(6,1,'queued',0,0,0,2,0,12,0,'batch_content_deletion','[203, 207]','2025-08-01 21:21:46',NULL,NULL,'2025-08-01 21:21:46'),(7,1,'queued',0,0,0,1,0,11,0,'batch_content_deletion','[203]','2025-08-01 21:22:46',NULL,NULL,'2025-08-01 21:22:46'),(8,1,'queued',0,0,0,2,0,12,0,'batch_content_deletion','[207, 202]','2025-08-01 21:26:11',NULL,NULL,'2025-08-01 21:26:11'),(9,1,'queued',0,0,0,1,0,11,0,'batch_content_deletion','[203]','2025-08-01 21:26:38',NULL,NULL,'2025-08-01 21:26:38'),(10,1,'completed',0,0,0,2,2,12,0,'completed','[207, 203]','2025-08-02 04:20:48','2025-08-02 04:20:48','2025-08-02 04:20:50','2025-08-02 04:20:50'),(11,1,'completed',0,0,0,1,1,11,0,'completed','[204]','2025-08-02 04:23:20','2025-08-02 04:23:20','2025-08-02 04:23:21','2025-08-02 04:23:21'),(12,1,'completed',0,0,0,1,1,11,0,'completed','[202]','2025-08-02 04:26:35','2025-08-02 04:26:35','2025-08-02 04:26:37','2025-08-02 04:26:37'),(13,1,'completed',0,2,0,4,4,15,0,'completed','[147, 209, 211, 213]','2025-08-02 12:17:43','2025-08-02 12:17:43','2025-08-02 12:17:48','2025-08-02 12:17:48'),(14,44,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:13',NULL,NULL,'2025-08-02 12:43:13'),(15,43,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:19',NULL,NULL,'2025-08-02 12:43:19'),(16,42,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:29',NULL,NULL,'2025-08-02 12:43:29'),(17,41,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:34',NULL,NULL,'2025-08-02 12:43:34'),(18,39,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:38',NULL,NULL,'2025-08-02 12:43:38'),(19,38,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:41',NULL,NULL,'2025-08-02 12:43:41'),(20,37,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:45',NULL,NULL,'2025-08-02 12:43:45'),(21,36,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:53',NULL,NULL,'2025-08-02 12:43:53'),(22,35,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:43:57',NULL,NULL,'2025-08-02 12:43:57'),(23,34,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:01',NULL,NULL,'2025-08-02 12:44:01'),(24,33,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:04',NULL,NULL,'2025-08-02 12:44:04'),(25,32,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:11',NULL,NULL,'2025-08-02 12:44:11'),(26,31,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:14',NULL,NULL,'2025-08-02 12:44:14'),(27,30,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:18',NULL,NULL,'2025-08-02 12:44:18'),(28,29,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:29',NULL,NULL,'2025-08-02 12:44:29'),(29,28,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:31',NULL,NULL,'2025-08-02 12:44:31'),(30,27,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:35',NULL,NULL,'2025-08-02 12:44:35'),(31,26,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:37',NULL,NULL,'2025-08-02 12:44:37'),(32,25,'queued',0,0,0,0,0,30,0,'pending',NULL,'2025-08-02 12:44:40',NULL,NULL,'2025-08-02 12:44:40');
/*!40000 ALTER TABLE `client_deletion_queue` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_mark_defaults`
--

DROP TABLE IF EXISTS `client_mark_defaults`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_mark_defaults` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `mark_name` varchar(100) NOT NULL,
  `default_values` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_client_mark` (`client_id`,`mark_name`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_mark_defaults`
--

LOCK TABLES `client_mark_defaults` WRITE;
/*!40000 ALTER TABLE `client_mark_defaults` DISABLE KEYS */;
INSERT INTO `client_mark_defaults` VALUES (1,1,'品牌名称','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\"]','2025-07-31 13:22:05','2025-07-31 13:22:30'),(2,1,'商品名称','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\"]','2025-07-31 13:22:05','2025-07-31 13:22:30'),(3,1,'店铺地址','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\"]','2025-07-31 13:22:05','2025-07-31 13:22:30'),(4,1,'标记5','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\"]','2025-07-31 13:22:30','2025-07-31 13:22:30'),(5,3,'品牌名称','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"啊啊啊\"]','2025-07-31 15:51:05','2025-07-31 15:51:05'),(6,3,'商品名称','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"我问问\"]','2025-07-31 15:51:05','2025-07-31 15:51:05'),(7,3,'店铺地址','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"额额额\"]','2025-07-31 15:51:05','2025-07-31 15:51:05'),(8,3,'标记2','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"热热热\"]','2025-07-31 15:51:05','2025-07-31 15:51:05'),(9,8,'品牌名称','[\"啊啊啊\"]','2025-08-02 14:07:19','2025-08-02 14:07:19'),(10,8,'商品名称','[\"额额额\"]','2025-08-02 14:07:19','2025-08-02 14:07:19'),(11,8,'店铺地址','[\"rrrr\"]','2025-08-02 14:07:19','2025-08-02 14:07:19'),(12,8,'标记2','[\"热热热\"]','2025-08-02 14:07:19','2025-08-02 14:07:19'),(13,6,'品牌名称','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','2025-08-02 14:20:31','2025-08-02 14:22:20'),(14,6,'商品名称','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\"]','2025-08-02 14:20:31','2025-08-02 14:22:20'),(15,6,'店铺地址','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','2025-08-02 14:20:31','2025-08-02 14:22:20'),(16,6,'标记2','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','2025-08-02 14:20:31','2025-08-02 14:22:20'),(17,6,'标记3','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','2025-08-02 14:20:31','2025-08-02 14:22:20');
/*!40000 ALTER TABLE `client_mark_defaults` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_share_links`
--

DROP TABLE IF EXISTS `client_share_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_share_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `share_key` varchar(64) NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `access_key` varchar(10) DEFAULT NULL COMMENT '访问密钥',
  `task_id` int(11) DEFAULT NULL COMMENT '限制访问的任务ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `share_key` (`share_key`),
  KEY `client_id` (`client_id`),
  KEY `expires_at` (`expires_at`),
  KEY `idx_client_share_links_access_key` (`access_key`),
  KEY `idx_client_share_links_task_id` (`task_id`),
  KEY `idx_client_share_links_client_id` (`client_id`),
  KEY `idx_client_share_links_share_key` (`share_key`),
  KEY `idx_client_share_links_is_active` (`is_active`),
  KEY `idx_client_share_links_expires_at` (`expires_at`),
  CONSTRAINT `client_share_links_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_client_share_links_task_id` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='客户分享链接表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_share_links`
--

LOCK TABLES `client_share_links` WRITE;
/*!40000 ALTER TABLE `client_share_links` DISABLE KEYS */;
INSERT INTO `client_share_links` VALUES (5,4,'826e36b5a2145216e526830c9c754c7e',NULL,1,'2025-07-30 21:31:46','2025-07-30 21:31:46','L2SU',NULL),(6,3,'aa0df85394972c7991bc5e1bc43e15c7',NULL,1,'2025-07-31 05:24:44','2025-07-31 05:24:44','L6F7',NULL),(12,1,'efc57b9a3582f807378228e7255e0c29',NULL,1,'2025-08-02 01:16:59','2025-08-02 01:16:59','CYP9',NULL);
/*!40000 ALTER TABLE `client_share_links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `contact` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `need_review` tinyint(1) DEFAULT '1',
  `daily_content_count` int(11) DEFAULT '5',
  `display_start_time` time DEFAULT NULL,
  `interval_min` int(11) DEFAULT '30',
  `interval_max` int(11) DEFAULT '120',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  `ext_json` text,
  `default_required_topics` text COMMENT '默认必选话题(JSON格式)',
  `default_random_topics` text COMMENT '默认随机话题(JSON格式)',
  `default_at_users` text COMMENT '默认@用户(JSON格式)',
  `default_location` varchar(200) DEFAULT NULL COMMENT '默认定位信息',
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前客户的所有任务中重复使用模板',
  `auto_approved` tinyint(1) DEFAULT '0' COMMENT '是否自动审核通过（无需客户审核）',
  `review_timeout_hours` int(11) DEFAULT '24' COMMENT '审核超时小时数',
  `review_deadline_time` time DEFAULT '20:00:00' COMMENT '每日审核截止时间',
  `auto_approve_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用自动通过',
  `delete_status` enum('active','deleting','delete_failed','deleted') DEFAULT 'active' COMMENT '删除状态：active=正常，deleting=删除中，delete_failed=删除失败，deleted=已删除',
  `delete_started_at` datetime DEFAULT NULL COMMENT '删除开始时间',
  `delete_progress` text COMMENT '删除进度信息，JSON格式存储',
  `delete_error_message` text COMMENT '删除失败时的错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_clients_delete_status` (`delete_status`),
  KEY `idx_clients_delete_started_at` (`delete_started_at`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'康师傅','','','',1,5,'08:30:00',10,30,'2025-07-14 02:17:45','2025-08-01 19:34:00',1,NULL,'[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','[\"@ee\", \"@rr\", \"@tt\", \"@yy\", \"@jj\", \"@ll\", \"@bb\"]','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]',0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(3,'蜜雪冰城','','','',1,5,'08:30:00',10,30,'2025-07-14 02:23:55','2025-07-28 04:19:01',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(4,'许府牛','','','',1,5,NULL,10,30,'2025-07-15 23:13:33','2025-07-22 23:50:04',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(6,'娃哈哈','饿','','',1,5,'08:30:00',10,30,'2025-07-15 23:19:13','2025-07-20 23:14:58',1,'{\"remark\": \"\\u997f\"}',NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(8,'海底捞','','','',1,5,'08:30:00',10,30,'2025-07-16 23:24:50','2025-07-20 19:02:22',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(22,'111','','','',1,5,'08:30:00',10,30,'2025-08-01 22:07:26','2025-08-01 22:07:26',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(23,'112','','','',1,5,'08:30:00',10,30,'2025-08-01 22:07:33','2025-08-01 22:07:33',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(24,'113','','','',1,5,'08:30:00',10,30,'2025-08-01 22:07:42','2025-08-01 22:07:42',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'active',NULL,NULL,NULL),(25,'114','','','',1,5,'08:30:00',10,30,'2025-08-01 22:07:50','2025-08-02 20:44:40',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:40','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:40.292831\"}',NULL),(26,'115','','','',1,5,'08:30:00',10,30,'2025-08-01 22:08:01','2025-08-02 20:44:38',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:38','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:37.878801\"}',NULL),(27,'116','','','',1,5,'08:30:00',10,30,'2025-08-01 22:08:09','2025-08-02 20:44:35',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:35','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:35.182353\"}',NULL),(28,'117','','','',1,5,'08:30:00',10,30,'2025-08-01 22:08:25','2025-08-02 20:44:32',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:32','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:31.759250\"}',NULL),(29,'117','','','',1,5,'08:30:00',10,30,'2025-08-01 22:08:35','2025-08-02 20:44:29',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:29','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:29.082197\"}',NULL),(30,'118','','','',1,5,'08:30:00',10,30,'2025-08-01 22:08:44','2025-08-02 20:44:18',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:18','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:18.467653\"}',NULL),(31,'119','','','',1,5,'08:30:00',10,30,'2025-08-01 22:08:53','2025-08-02 20:44:15',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:15','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:14.871249\"}',NULL),(32,'120','','','',1,5,'08:30:00',10,30,'2025-08-01 22:09:01','2025-08-02 20:44:12',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:12','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:11.679357\"}',NULL),(33,'121','','','',1,5,'08:30:00',10,30,'2025-08-01 22:09:22','2025-08-02 20:44:05',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:05','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:04.766274\"}',NULL),(34,'122','','','',1,5,'08:30:00',10,30,'2025-08-01 22:09:47','2025-08-02 20:44:02',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:44:02','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:44:01.675172\"}',NULL),(35,'123','','','',1,5,'08:30:00',10,30,'2025-08-01 22:09:56','2025-08-02 20:43:57',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:57','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:57.413482\"}',NULL),(36,'124','','','',1,5,'08:30:00',10,30,'2025-08-01 22:10:05','2025-08-02 20:43:53',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:53','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:53.100569\"}',NULL),(37,'125','','','',1,5,'08:30:00',10,30,'2025-08-01 22:10:13','2025-08-02 20:43:45',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:45','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:45.136761\"}',NULL),(38,'126','','','',1,5,'08:30:00',10,30,'2025-08-01 22:10:22','2025-08-02 20:43:42',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:42','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:41.847316\"}',NULL),(39,'127','','','',1,5,'08:30:00',10,30,'2025-08-01 22:10:32','2025-08-02 20:43:38',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:38','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:38.470890\"}',NULL),(40,'128','','','',1,5,'08:30:00',10,30,'2025-08-01 22:10:55','2025-08-02 05:03:48',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 05:03:48','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T05:03:48.021818\"}',NULL),(41,'129','','','',1,5,'08:30:00',10,30,'2025-08-01 22:11:04','2025-08-02 20:43:34',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:34','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:34.189957\"}',NULL),(42,'1112','','','',1,5,'08:30:00',10,30,'2025-08-02 19:07:09','2025-08-02 20:43:30',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:30','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:29.835386\"}',NULL),(43,'二位','','','',1,5,'08:30:00',10,30,'2025-08-02 19:17:40','2025-08-02 20:43:19',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:19','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:19.128381\"}',NULL),(44,'是的冯绍峰','','','',1,5,'08:30:00',10,30,'2025-08-02 19:30:27','2025-08-02 20:43:13',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1,'deleting','2025-08-02 20:43:13','{\"stage\": \"queued\", \"total_images\": 0, \"total_contents\": 0, \"total_tasks\": 0, \"total_batches\": 0, \"deleted_images\": 0, \"deleted_contents\": 0, \"deleted_tasks\": 0, \"deleted_batches\": 0, \"estimated_duration\": 30, \"started_at\": \"2025-08-02T20:43:13.456551\"}',NULL);
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `content_history`
--

DROP TABLE IF EXISTS `content_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `content_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `editor_id` int(11) DEFAULT NULL,
  `edit_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_client_edit` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `editor_id` (`editor_id`),
  KEY `idx_content_history_content_id` (`content_id`),
  CONSTRAINT `content_history_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `content_history_ibfk_2` FOREIGN KEY (`editor_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `content_history`
--

LOCK TABLES `content_history` WRITE;
/*!40000 ALTER TABLE `content_history` DISABLE KEYS */;
INSERT INTO `content_history` VALUES (1,201,'节日限定✅ ttww错过等一年！','✅ ee✅ 去\r\n圣诞季必吃的限定款ww！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食22',NULL,'2025-08-02 01:18:07',1);
/*!40000 ALTER TABLE `content_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `content_images`
--

DROP TABLE IF EXISTS `content_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `content_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `image_path` varchar(500) NOT NULL,
  `thumbnail_path` varchar(500) DEFAULT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `image_order` int(11) DEFAULT '1' COMMENT '图片排序',
  `upload_time` datetime DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0',
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `upload_time` (`upload_time`),
  KEY `idx_content_images_content_id` (`content_id`),
  CONSTRAINT `content_images_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4 COMMENT='文案图片管理表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `content_images`
--

LOCK TABLES `content_images` WRITE;
/*!40000 ALTER TABLE `content_images` DISABLE KEYS */;
INSERT INTO `content_images` VALUES (1,161,'images/202508/content_161_1754039578_f2f088bf.webp','thumbnails/202508/content_161_1754039578_f2f088bf_thumb.webp','641_2.webp',233472,1,'2025-08-01 17:12:59',0,NULL),(2,161,'images/202508/content_161_1754039578_0df8d941.webp','thumbnails/202508/content_161_1754039578_0df8d941_thumb.webp','641.webp',98792,1,'2025-08-01 17:12:59',0,NULL),(3,161,'images/202508/content_161_1754039578_9bc54984.webp','thumbnails/202508/content_161_1754039578_9bc54984_thumb.webp','4062d50c69a04f5389f9fd56ea010979.webp',86254,1,'2025-08-01 17:12:59',0,NULL),(24,201,'images/202508/content_201_1754048963_7d7c00c3.webp','thumbnails/202508/content_201_1754048963_7d7c00c3_thumb.webp','4062d50c69a04f5389f9fd56ea010979.webp',86254,1,'2025-08-01 19:49:23',0,NULL),(28,205,'images/202508/content_205_1754049201_b022fd49.webp','thumbnails/202508/content_205_1754049201_b022fd49_thumb.webp','srchttp3A2F2Fsafe-img.xhscdn.com2Fbw12F37da12de-4eb6-49e1-8732-ece100730b283FimageView22F22Fw2F10802Fformat2Fjpgreferhttp3A2F2Fsafe-img.xhscdn.webp',63046,1,'2025-08-01 19:53:21',0,NULL),(30,192,'images/202508/content_192_1754049218_287117b7.webp','thumbnails/202508/content_192_1754049218_287117b7_thumb.webp','srchttp3A2F2Fsafe-img.xhscdn.com2Fbw12Fab005d64-c66a-4d0c-a53c-eb7ae74f849b3FimageView22F22Fw2F10802Fformat2Fjpgreferhttp3A2F2Fsafe-img.xhscdn.webp',65230,1,'2025-08-01 19:53:38',1,'2025-08-02 12:56:29'),(31,193,'images/202508/content_193_1754049223_dbd248b2.jpg','thumbnails/202508/content_193_1754049223_dbd248b2_thumb.jpg','u10835818623078470118fm253app138fJPEG_w1197h800.jpg',141604,1,'2025-08-01 19:53:44',0,NULL),(32,195,'images/202508/content_195_1754049231_c31975ba.jpg','thumbnails/202508/content_195_1754049231_c31975ba_thumb.jpg','u12440738792477402592fm253app138fJPEG_w800h1311.jpg',180573,1,'2025-08-01 19:53:52',0,NULL),(37,196,'images/202508/content_196_1754064333_6a610635.jpg','thumbnails/202508/content_196_1754064333_6a610635_thumb.jpg','8111b271ly1hrmiemvk8jj20ww17tk1k.jpg',151362,1,'2025-08-02 00:05:33',0,NULL),(38,197,'images/202508/content_197_1754064339_b7e9dcd8.webp','thumbnails/202508/content_197_1754064339_b7e9dcd8_thumb.webp','abc0ff7c1ba74fc6999ad026fe2e33c0.webp',26436,1,'2025-08-02 00:05:40',0,NULL),(39,198,'images/202508/content_198_1754064347_8b23ef2f.webp','thumbnails/202508/content_198_1754064347_8b23ef2f_thumb.webp','641_2.webp',233472,1,'2025-08-02 00:05:47',0,NULL),(41,199,'images/202508/content_199_1754064553_626f55cd.webp','thumbnails/202508/content_199_1754064553_626f55cd_thumb.webp','4062d50c69a04f5389f9fd56ea010979.webp',86254,1,'2025-08-02 00:09:13',0,NULL),(42,184,'images/202508/content_184_1754065645_27b21834.jpg','thumbnails/202508/content_184_1754065645_27b21834_thumb.jpg','8111b271ly1hrmiemvk8jj20ww17tk1k.jpg',151362,1,'2025-08-02 00:27:26',1,'2025-08-02 01:07:07'),(43,186,'images/202508/content_186_1754065653_e0762ee0.webp','thumbnails/202508/content_186_1754065653_e0762ee0_thumb.webp','abc0ff7c1ba74fc6999ad026fe2e33c0.webp',26436,1,'2025-08-02 00:27:34',0,NULL),(44,185,'images/202508/content_185_1754065680_8b9a44c7.webp','thumbnails/202508/content_185_1754065680_8b9a44c7_thumb.webp','641_2.webp',233472,1,'2025-08-02 00:28:01',1,'2025-08-02 01:07:28'),(45,188,'images/202508/content_188_1754065892_9076018d.png','thumbnails/202508/content_188_1754065892_9076018d_thumb.png','ab6e7e8258937a49879994cd7d61d7c7.png',990363,1,'2025-08-02 00:31:33',0,NULL),(46,190,'images/202508/content_190_1754066163_37dcf838.jpg','thumbnails/202508/content_190_1754066163_37dcf838_thumb.jpg','99a5476114ffb35708b553d13f047c4a7736.jpg',1796646,1,'2025-08-02 00:36:03',0,NULL),(47,185,'images/202508/content_185_1754066469_287763b6.webp','thumbnails/202508/content_185_1754066469_287763b6_thumb.webp','abc0ff7c1ba74fc6999ad026fe2e33c0.webp',26436,2,'2025-08-02 00:41:10',0,NULL),(48,186,'images/202508/content_186_1754066709_2c618f1f.png','thumbnails/202508/content_186_1754066709_2c618f1f_thumb.png','ab6e7e8258937a49879994cd7d61d7c7.png',990363,2,'2025-08-02 00:45:10',1,'2025-08-02 01:03:30'),(49,184,'images/202508/content_184_1754066906_1fd28111.webp','thumbnails/202508/content_184_1754066906_1fd28111_thumb.webp','srchttp3A2F2Fsafe-img.xhscdn.com2Fbw12F712411ee-c16f-429f-9814-02c658e251543FimageView22F22Fw2F10802Fformat2Fjpgreferhttp3A2F2Fsafe-img.xhscdn.webp',53412,2,'2025-08-02 00:48:27',1,'2025-08-02 01:07:09'),(50,184,'images/202508/content_184_1754067323_7a0b768d.webp','thumbnails/202508/content_184_1754067323_7a0b768d_thumb.webp','srchttp3A2F2Fsafe-img.xhscdn.com2Fbw12F41f0869c-7939-4ee6-86b7-511459ffee333FimageView22F22Fw2F10802Fformat2Fjpgreferhttp3A2F2Fsafe-img.xhscdn.webp',47596,3,'2025-08-02 00:55:24',1,'2025-08-02 12:56:29'),(51,186,'images/202508/content_186_1754067540_ae46193c.png','thumbnails/202508/content_186_1754067540_ae46193c_thumb.png','ab6e7e8258937a49879994cd7d61d7c7.png',990363,3,'2025-08-02 00:59:00',1,'2025-08-02 00:59:09'),(52,185,'images/202508/content_185_1754067627_2623f425.jpg','thumbnails/202508/content_185_1754067627_2623f425_thumb.jpg','559_a47042760a31963482278cf4e7543446a45aabb7.jpg',292884,3,'2025-08-02 01:00:28',1,'2025-08-02 01:07:31'),(53,185,'images/202508/content_185_1754067627_b31521a2.png','thumbnails/202508/content_185_1754067627_b31521a2_thumb.png','ab6e7e8258937a49879994cd7d61d7c7.png',990363,3,'2025-08-02 01:00:28',1,'2025-08-02 01:07:33'),(54,185,'images/202508/content_185_1754067627_af7f7782.webp','thumbnails/202508/content_185_1754067627_af7f7782_thumb.webp','641_1.webp',62760,3,'2025-08-02 01:00:28',0,NULL),(55,185,'images/202508/content_185_1754067627_cad2a5b1.webp','thumbnails/202508/content_185_1754067627_cad2a5b1_thumb.webp','abc0ff7c1ba74fc6999ad026fe2e33c0.webp',26436,3,'2025-08-02 01:00:28',1,'2025-08-02 01:13:07'),(56,187,'images/202508/content_187_1754068517_08d4fc27.jpg','thumbnails/202508/content_187_1754068517_08d4fc27_thumb.jpg','8111b271ly1hrmiemvk8jj20ww17tk1k.jpg',151362,1,'2025-08-02 01:15:18',0,NULL),(57,201,'images/202508/content_201_1754068680_00bdbec3.jpg','thumbnails/202508/content_201_1754068680_00bdbec3_thumb.jpg','8111b271ly1hrmiemvk8jj20ww17tk1k.jpg',151362,2,'2025-08-02 01:18:01',0,NULL),(58,201,'images/202508/content_201_1754071530_85a26d1a.webp','thumbnails/202508/content_201_1754071530_85a26d1a_thumb.webp','334fdda87f23190e79873c16551af00e_u14902372184115737545fm3028app3028fJPEGfmtauto_w1280h720.webp',348390,3,'2025-08-02 02:05:30',0,NULL),(59,201,'images/202508/content_201_1754071530_c39faf4d.jpg','thumbnails/202508/content_201_1754071530_c39faf4d_thumb.jpg','559_a47042760a31963482278cf4e7543446a45aabb7.jpg',292884,3,'2025-08-02 02:05:30',0,NULL),(60,201,'images/202508/content_201_1754071530_b2f2b57c.jpg','thumbnails/202508/content_201_1754071530_b2f2b57c_thumb.jpg','8111b271ly1hrmiemvk8jj20ww17tk1k.jpg',151362,3,'2025-08-02 02:05:30',0,NULL),(61,201,'images/202508/content_201_1754071530_c741475c.png','thumbnails/202508/content_201_1754071530_c741475c_thumb.png','ab6e7e8258937a49879994cd7d61d7c7.png',990363,3,'2025-08-02 02:05:30',0,NULL),(62,201,'images/202508/content_201_1754071530_a1d34539.webp','thumbnails/202508/content_201_1754071530_a1d34539_thumb.webp','641_1.webp',62760,3,'2025-08-02 02:05:31',0,NULL),(63,201,'images/202508/content_201_1754071530_51c0db6d.webp','thumbnails/202508/content_201_1754071530_51c0db6d_thumb.webp','abc0ff7c1ba74fc6999ad026fe2e33c0.webp',26436,3,'2025-08-02 02:05:31',0,NULL),(64,189,'images/202508/content_189_1754075729_1981948d.jpg','thumbnails/202508/content_189_1754075729_1981948d_thumb.jpg','68ec17f5-9621-461f-ad22-a6820a3f9cf5.jpg',841679,1,'2025-08-02 03:15:29',0,NULL),(65,176,'images/202508/content_176_1754075736_054dcdcf.jpg','thumbnails/202508/content_176_1754075736_054dcdcf_thumb.jpg','68ec17f5-9621-461f-ad22-a6820a3f9cf5.jpg',841679,1,'2025-08-02 03:15:37',0,NULL),(66,177,'images/202508/content_177_1754075745_9962d9b6.webp','thumbnails/202508/content_177_1754075745_9962d9b6_thumb.webp','abc0ff7c1ba74fc6999ad026fe2e33c0.webp',26436,1,'2025-08-02 03:15:46',0,NULL),(67,178,'images/202508/content_178_1754075753_06eba7b9.webp','thumbnails/202508/content_178_1754075753_06eba7b9_thumb.webp','641_2.webp',233472,1,'2025-08-02 03:15:53',0,NULL),(68,179,'images/202508/content_179_1754075760_b3d35a38.webp','thumbnails/202508/content_179_1754075760_b3d35a38_thumb.webp','srchttp3A2F2Fsafe-img.xhscdn.com2Fbw12F28c660b7-e23d-4f74-8c72-e3f75171b8c53FimageView22F22Fw2F10802Fformat2Fjpgreferhttp3A2F2Fsafe-img.xhscdn.webp',38694,1,'2025-08-02 03:16:00',0,NULL),(69,180,'images/202508/content_180_1754078674_40110408.webp','thumbnails/202508/content_180_1754078674_40110408_thumb.webp','u14902372184115737545fm3028app3028fJPEGfmtauto.webp',42578,1,'2025-08-02 04:04:34',0,NULL);
/*!40000 ALTER TABLE `content_images` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `contents`
--

DROP TABLE IF EXISTS `contents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) DEFAULT NULL,
  `task_id` int(11) DEFAULT NULL,
  `batch_id` int(11) DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `topics` text,
  `location` varchar(100) DEFAULT NULL,
  `image_urls` text,
  `display_date` date DEFAULT NULL,
  `display_time` time DEFAULT NULL,
  `workflow_status` varchar(30) DEFAULT 'draft',
  `publish_status` varchar(30) DEFAULT 'unpublished',
  `client_review_status` varchar(20) DEFAULT 'pending',
  `internal_review_status` varchar(20) DEFAULT 'pending',
  `publish_priority` varchar(10) DEFAULT 'normal',
  `publish_time` datetime DEFAULT NULL,
  `status_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `reviewer_id` int(11) DEFAULT NULL,
  `review_time` datetime DEFAULT NULL,
  `ext_json` text,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `pending_image_fix` tinyint(1) DEFAULT '0' COMMENT '是否有待处理的图片问题',
  `content_completed` tinyint(1) DEFAULT '1' COMMENT '文案是否已重新编辑完成(0:未完成, 1:已完成)',
  `image_completed` tinyint(1) DEFAULT '1' COMMENT '图片是否已重新上传完成(0:未完成, 1:已完成)',
  `publish_error` text COMMENT '发布提示信息（成功或失败的详细信息）',
  `publish_retry_count` int(11) DEFAULT '0' COMMENT '发布重试次数',
  `image_editor_id` int(11) DEFAULT NULL COMMENT '图片编辑管理员ID',
  `content_editor_id` int(11) DEFAULT NULL COMMENT '文案编辑管理员ID（初审）',
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `task_id` (`task_id`),
  KEY `batch_id` (`batch_id`),
  KEY `template_id` (`template_id`),
  KEY `created_by` (`created_by`),
  KEY `reviewer_id` (`reviewer_id`),
  KEY `ix_contents_workflow_status` (`workflow_status`),
  KEY `ix_contents_publish_status` (`publish_status`),
  KEY `ix_contents_publish_priority` (`publish_priority`),
  KEY `ix_contents_is_deleted` (`is_deleted`),
  KEY `deleted_by` (`deleted_by`),
  KEY `idx_workflow_status` (`workflow_status`),
  KEY `idx_client_review_status` (`client_review_status`),
  KEY `idx_internal_review_status` (`internal_review_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_contents_completion` (`content_completed`,`image_completed`),
  KEY `idx_contents_workflow_status` (`workflow_status`),
  KEY `idx_contents_client_id` (`client_id`),
  KEY `idx_contents_task_id` (`task_id`),
  KEY `idx_contents_batch_id` (`batch_id`),
  KEY `idx_contents_created_at` (`created_at`),
  KEY `idx_contents_display_date` (`display_date`),
  KEY `idx_contents_publish_time` (`publish_time`),
  KEY `idx_contents_client_review_status` (`client_review_status`),
  KEY `idx_contents_workflow_client` (`workflow_status`,`client_id`),
  KEY `idx_contents_workflow_created` (`workflow_status`,`created_at`),
  KEY `idx_contents_client_created` (`client_id`,`created_at`),
  KEY `idx_contents_image_editor_id` (`image_editor_id`),
  KEY `idx_contents_content_editor_id` (`content_editor_id`),
  CONSTRAINT `contents_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `contents_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `contents_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`),
  CONSTRAINT `contents_ibfk_4` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`),
  CONSTRAINT `contents_ibfk_5` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_6` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_7` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_contents_content_editor` FOREIGN KEY (`content_editor_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_contents_image_editor` FOREIGN KEY (`image_editor_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=232 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contents`
--

LOCK TABLES `contents` WRITE;
/*!40000 ALTER TABLE `contents` DISABLE KEYS */;
INSERT INTO `contents` VALUES (148,1,NULL,NULL,97,'深夜放毒时间到！✅ wwrr太罪恶了✅ ','✅ ww\r\n半夜饿到不行发现ww还营业！✅ rr热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"tt\", \"yy\"]','rr',NULL,'2025-07-31','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:33','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@ee\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(149,1,NULL,NULL,101,'减肥也能吃！✅ rr轻食系列yy太✅ 了','✅ ee\r\n健身教练推荐的yy，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"yy\", \"tt\"]','jj',NULL,'2025-07-31','08:55:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:33','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@jj\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(150,1,NULL,NULL,99,'✅ 打工人午餐救星！yyww15分钟上菜⚡','✅ tt✅ \r\n工作日中午的快乐源泉～ww量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"ee\", \"tt\"]','yy',NULL,'2025-07-31','09:18:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:33','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@jj\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(151,1,NULL,NULL,94,'✅ 挖到宝了✨yy的yy也太香了吧！','✅ ee✅ \r\n路过被香味吸引进店，yy直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"tt\", \"yy\"]','jj',NULL,'2025-07-31','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:55','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@yy\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(152,1,NULL,NULL,102,'节日限定✅ wwyy错过等一年！','✅ ee✅ 去\r\n圣诞季必吃的限定款yy！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"tt\", \"bb\"]','ll',NULL,'2025-07-31','08:50:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:55','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@yy\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(153,1,NULL,NULL,93,'rr必点单品！✅ tt一口就爱上✅ ','✅ ee✅ \r\n今天终于吃到rr的招牌tt，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"ll\", \"jj\"]','yy',NULL,'2025-07-31','09:10:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:55','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@ee\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(154,1,NULL,NULL,98,'✅ 闺蜜下午茶首选?✅ rrtt颜值味道双在线！','✅  ww✅ \r\n和姐妹约会的秘密基地！tt不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"rr\", \"ll\"]','yy',NULL,'2025-07-31','09:21:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:55','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@tt\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(155,1,NULL,NULL,100,'家庭聚餐就选这✅ wwyy老少皆宜✅ ','✅ ee\r\n带全家来吃ww，✅ yy获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"yy\", \"bb\", \"ll\"]','yy',NULL,'2025-07-31','10:03:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 15:06:41','2025-07-31 23:52:55','2025-08-01 15:06:41',1,NULL,NULL,'{\"at_users\": [\"@tt\"]}',1,'2025-08-01 15:06:41',NULL,0,1,1,NULL,0,2,2),(156,3,NULL,NULL,95,'本地人才知道的隐藏美味！✅ yyrr✅ ','✅ ee✅ \r\n同事强烈安利的rr，果然没让我失望！独家秘制配方，吃完还想打包十份带走！ #地方美食 #必吃清单','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"tt\", \"bb\"]','ll',NULL,'2025-07-31','08:30:00','first_reviewed','unpublished','pending','first_approved','normal',NULL,'2025-08-02 20:36:39','2025-07-31 23:53:15','2025-08-02 20:36:39',1,2,'2025-08-01 17:53:08','{\"at_users\": [\"@yy\"]}',1,'2025-08-02 20:36:39',NULL,0,1,1,NULL,0,2,2),(157,3,NULL,NULL,96,'早餐新选择！✅ wwtt开启元气一天☀️','✅ yy\r\n早起就是为了这口tt！现点现做，酥脆爆浆，搭配咖啡绝绝子～打工人早餐首选 #早餐推荐 #美食日记','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"jj\", \"tt\"]','ll',NULL,'2025-07-31','08:47:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:36:39','2025-07-31 23:53:15','2025-08-02 20:36:39',1,NULL,NULL,'{\"at_users\": [\"@ee\"]}',1,'2025-08-02 20:36:39',NULL,0,1,1,NULL,0,2,2),(158,3,NULL,NULL,99,'✅ 打工人午餐救星！eerr15分钟上菜⚡','✅ rr✅ \r\n工作日中午的快乐源泉～rr量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"yy\", \"tt\", \"jj\"]','jj',NULL,'2025-07-31','08:30:00','pending_client_review','unpublished','pending','approved','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:53:58','2025-08-02 20:42:23',1,1,'2025-08-01 23:47:26','{\"at_users\": [\"@jj\", \"@yy\", \"@tt\"]}',1,'2025-08-02 20:42:23',NULL,0,1,1,NULL,0,2,2),(159,3,NULL,NULL,101,'减肥也能吃！✅ ww轻食系列我问问太✅ 了','✅ yy\r\n健身教练推荐的我问问，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"bb\", \"ee\"]','tt',NULL,'2025-07-31','08:52:00','pending_client_review','unpublished','pending','approved','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:53:58','2025-08-02 20:42:23',1,1,'2025-08-01 23:47:26','{\"at_users\": [\"@jj\", \"@rr\", \"@ll\"]}',1,'2025-08-02 20:42:23',NULL,0,1,1,NULL,0,2,2),(160,3,NULL,NULL,102,'节日限定✅ ttyy错过等一年！','✅ yy✅ 去\r\n圣诞季必吃的限定款yy！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"tt\", \"yy\"]','jj',NULL,'2025-07-31','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:54:26','2025-08-02 20:42:23',1,NULL,NULL,'{\"at_users\": [\"@ee\"]}',1,'2025-08-01 17:11:13',1,0,1,1,NULL,0,2,2),(161,3,NULL,NULL,93,'啊啊啊必点单品！✅ yy一口就爱上✅ ','✅ ee✅ \r\n今天终于吃到啊啊啊的招牌yy，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"ll\", \"bb\"]','ll',NULL,'2025-07-31','08:58:00','publishing','publishing','approved','approved','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:54:26','2025-08-02 20:42:23',1,1,'2025-08-01 23:16:31','{\"at_users\": [\"@jj\"]}',1,'2025-08-02 12:38:44',1,0,1,1,NULL,0,2,2),(162,3,NULL,NULL,97,'深夜放毒时间到！✅ ttyy太罪恶了✅ ','✅ yy\r\n半夜饿到不行发现tt还营业！✅ yy热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"tt\", \"yy\"]','bb',NULL,'2025-07-31','09:09:00','pending_client_review','unpublished','pending','approved','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:54:26','2025-08-02 20:42:23',1,1,'2025-08-01 23:47:26','{\"at_users\": [\"@rr\"]}',1,'2025-08-02 20:42:23',NULL,0,1,1,NULL,0,2,2),(163,3,NULL,NULL,100,'家庭聚餐就选这✅ ww我问问老少皆宜✅ ','✅ rr\r\n带全家来吃ww，✅ 我问问获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"tt\", \"jj\"]','jj',NULL,'2025-07-31','09:43:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:54:26','2025-08-02 20:42:23',1,2,'2025-08-01 19:22:42','{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:42:23',NULL,0,1,1,NULL,0,2,2),(164,3,NULL,NULL,94,'✅ 挖到宝了✨ww的rr也太香了吧！','✅ 额额额✅ \r\n路过被香味吸引进店，rr直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"yy\", \"jj\"]','tt',NULL,'2025-07-31','09:40:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:54:26','2025-08-02 20:42:23',1,NULL,NULL,'{\"at_users\": [\"@bb\"]}',1,'2025-08-01 17:11:19',1,0,1,1,NULL,0,2,2),(165,3,NULL,NULL,98,'✅ 闺蜜下午茶首选?✅ ee我问问颜值味道双在线！','✅  额额额✅ \r\n和姐妹约会的秘密基地！我问问不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"yy\", \"ll\"]','bb',NULL,'2025-08-01','08:30:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 20:42:23','2025-07-31 23:54:26','2025-08-02 20:42:23',1,NULL,NULL,'{\"at_users\": [\"@tt\"]}',1,'2025-08-02 05:04:44',1,0,1,1,NULL,0,2,2),(166,1,NULL,NULL,95,'本地人才知道的隐藏美味！✅ rrww✅ ','✅ ww✅ \r\n同事强烈安利的ww，果然没让我失望！独家秘制配方，吃完还想打包十份带走！ #地方美食 #必吃清单','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"yy\", \"jj\"]','jj',NULL,'2025-08-01','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 14:58:11','2025-08-01 06:09:24','2025-08-01 14:58:11',1,NULL,NULL,'{\"at_users\": [\"@jj\"]}',1,'2025-08-01 14:58:11',NULL,0,1,1,NULL,0,2,2),(167,1,NULL,NULL,96,'早餐新选择！✅ rrww开启元气一天☀️','✅ rr\r\n早起就是为了这口ww！现点现做，酥脆爆浆，搭配咖啡绝绝子～打工人早餐首选 #早餐推荐 #美食日记','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"ee\", \"rr\"]','ll',NULL,'2025-08-01','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-01 14:58:11','2025-08-01 06:14:41','2025-08-01 14:58:11',1,NULL,NULL,'{\"at_users\": [\"@bb\"]}',1,'2025-08-01 14:58:11',NULL,0,1,1,NULL,0,2,2),(168,1,NULL,NULL,98,'✅ 闺蜜下午茶首选?✅ wwww颜值味道双在线！','✅  rr✅ \r\n和姐妹约会的秘密基地！ww不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"yy\", \"ll\"]','bb',NULL,'2025-08-01','08:30:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(169,1,NULL,NULL,97,'深夜放毒时间到！✅ wwtt太罪恶了✅ ','✅ yy\r\n半夜饿到不行发现ww还营业！✅ tt热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"tt\", \"ee\"]','bb',NULL,'2025-08-01','08:47:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@bb\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(170,1,NULL,NULL,100,'家庭聚餐就选这✅ rrtt老少皆宜✅ ','✅ ww\r\n带全家来吃rr，✅ tt获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"tt\", \"ee\"]','tt',NULL,'2025-08-01','09:05:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(171,1,NULL,NULL,102,'节日限定✅ eeyy错过等一年！','✅ rr✅ 去\r\n圣诞季必吃的限定款yy！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"rr\", \"ll\"]','yy',NULL,'2025-08-01','09:36:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@ee\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(172,1,NULL,NULL,94,'✅ 挖到宝了✨ee的tt也太香了吧！','✅ tt✅ \r\n路过被香味吸引进店，tt直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"bb\", \"jj\"]','ll',NULL,'2025-08-01','09:40:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@bb\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(173,1,NULL,NULL,93,'ww必点单品！✅ yy一口就爱上✅ ','✅ rr✅ \r\n今天终于吃到ww的招牌yy，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"ll\", \"yy\"]','bb',NULL,'2025-08-02','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@tt\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(174,1,NULL,NULL,101,'减肥也能吃！✅ yy轻食系列yy太✅ 了','✅ tt\r\n健身教练推荐的yy，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"bb\", \"rr\"]','jj',NULL,'2025-08-02','08:51:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@bb\"]}',1,'2025-08-02 20:18:19',NULL,0,1,1,NULL,0,2,2),(175,1,NULL,NULL,99,'✅ 打工人午餐救星！yyee15分钟上菜⚡','✅ ee✅ \r\n工作日中午的快乐源泉～ee量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"ll\", \"tt\"]','tt',NULL,'2025-08-02','09:19:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:40:21','2025-08-01 19:34:39','2025-08-02 20:40:21',1,NULL,NULL,'{\"at_users\": [\"@tt\"]}',1,'2025-08-02 20:18:19',NULL,0,1,1,NULL,0,2,2),(176,1,NULL,NULL,100,'家庭聚餐就选这✅ ttyy老少皆宜✅ ','✅ ee\r\n带全家来吃tt，✅ yy获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"jj\", \"yy\"]','bb',NULL,'2025-08-01','08:30:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,2,'2025-08-02 03:14:31','{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(177,1,NULL,NULL,99,'✅ 打工人午餐救星！wwyy15分钟上菜⚡','✅ tt✅ \r\n工作日中午的快乐源泉～yy量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"bb\", \"tt\"]','rr',NULL,'2025-08-01','08:54:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,2,'2025-08-02 03:14:31','{\"at_users\": [\"@jj\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(178,1,NULL,NULL,97,'深夜放毒时间到！✅ wwrr太罪恶了✅ ','✅ yy\r\n半夜饿到不行发现ww还营业！✅ rr热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"jj\", \"tt\"]','bb',NULL,'2025-08-01','09:10:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,2,'2025-08-02 03:14:31','{\"at_users\": [\"@bb\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(179,1,NULL,NULL,101,'减肥也能吃！✅ rr轻食系列ww太✅ 了','✅ ww\r\n健身教练推荐的ww，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"yy\", \"jj\"]','ee',NULL,'2025-08-01','09:12:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,2,'2025-08-02 03:14:31','{\"at_users\": [\"@ee\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(180,1,NULL,NULL,94,'✅ 挖到宝了✨tt的tt也太香了吧！','✅ ee✅ \r\n路过被香味吸引进店，tt直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"yy\", \"ll\"]','jj',NULL,'2025-08-01','09:39:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,2,'2025-08-02 03:14:31','{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(181,1,NULL,NULL,93,'ww必点单品！✅ tt一口就爱上✅ ','✅ tt✅ \r\n今天终于吃到ww的招牌tt，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"rr\", \"bb\"]','ll',NULL,'2025-08-02','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,NULL,NULL,'{\"at_users\": [\"@rr\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(182,1,NULL,NULL,102,'节日限定✅ eeww错过等一年！','✅ ee✅ 去\r\n圣诞季必吃的限定款ww！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"jj\", \"ll\"]','ll',NULL,'2025-08-02','08:41:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,NULL,NULL,'{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(183,1,NULL,NULL,98,'✅ 闺蜜下午茶首选?✅ yyrr颜值味道双在线！','✅  tt✅ \r\n和姐妹约会的秘密基地！rr不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"bb\", \"jj\"]','rr',NULL,'2025-08-02','09:23:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 20:23:10','2025-08-01 19:35:07','2025-08-02 20:23:10',1,NULL,NULL,'{\"at_users\": [\"@ee\"]}',1,'2025-08-02 20:18:11',NULL,0,1,1,NULL,0,2,2),(184,1,NULL,NULL,99,'✅ 打工人午餐救星！rrww15分钟上菜⚡','✅ tt✅ \r\n工作日中午的快乐源泉～ww量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"yy\", \"ll\"]','bb',NULL,'2025-08-01','08:30:00','ready_to_publish','unpublished','approved','approved','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,1,'2025-08-02 02:47:27','{\"at_users\": [\"@yy\"]}',1,'2025-08-02 12:56:29',1,0,1,1,NULL,0,2,2),(185,1,NULL,NULL,94,'✅ 挖到宝了✨tt的rr也太香了吧！','✅ ww✅ \r\n路过被香味吸引进店，rr直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"ll\", \"bb\"]','bb',NULL,'2025-08-01','08:42:00','published','published','approved','approved','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,1,'2025-08-02 03:17:46','{\"at_users\": [\"@yy\"]}',1,'2025-08-02 14:11:34',1,0,1,1,'批量标记为失败',0,2,2),(186,1,NULL,NULL,102,'节日限定✅ yyrr错过等一年！','✅ ww✅ 去\r\n圣诞季必吃的限定款rr！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"jj\", \"rr\"]','yy',NULL,'2025-08-01','09:18:00','published','published','approved','approved','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,1,'2025-08-02 03:17:47','{\"at_users\": [\"@bb\"]}',1,'2025-08-02 14:11:40',1,0,1,1,NULL,0,2,2),(187,1,NULL,NULL,100,'家庭聚餐就选这✅ ttrr老少皆宜✅ ','✅ ww\r\n带全家来吃tt，✅ rr获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"tt\", \"bb\"]','rr',NULL,'2025-08-01','09:45:00','pending_publish','pending_publish','approved','approved','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,1,'2025-08-02 03:17:47','{\"at_users\": [\"@rr\"]}',1,'2025-08-02 20:17:03',NULL,0,1,1,NULL,0,2,2),(188,1,NULL,NULL,101,'减肥也能吃！✅ rr轻食系列rr太✅ 了','✅ tt\r\n健身教练推荐的rr，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"rr\", \"yy\"]','ll',NULL,'2025-08-01','09:51:00','pending_publish','pending_publish','approved','approved','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,1,'2025-08-02 03:17:47','{\"at_users\": [\"@tt\"]}',1,'2025-08-02 20:17:03',NULL,0,1,1,NULL,0,2,2),(189,1,NULL,NULL,97,'深夜放毒时间到！✅ rrrr太罪恶了✅ ','✅ ee\r\n半夜饿到不行发现rr还营业！✅ rr热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"yy\", \"bb\", \"jj\"]','rr',NULL,'2025-08-02','08:30:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,2,'2025-08-02 00:08:37','{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:17:03',NULL,0,1,1,NULL,0,2,2),(190,1,NULL,NULL,93,'rr必点单品！✅ rr一口就爱上✅ ','✅ ww✅ \r\n今天终于吃到rr的招牌rr，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"rr\", \"jj\"]','ll',NULL,'2025-08-02','08:47:00','final_review','unpublished','pending','pending','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,2,'2025-08-02 00:08:37','{\"at_users\": [\"@tt\"]}',1,'2025-08-02 20:17:03',NULL,0,1,1,NULL,0,2,2),(191,1,NULL,NULL,98,'✅ 闺蜜下午茶首选?✅ ttyy颜值味道双在线！','✅  tt✅ \r\n和姐妹约会的秘密基地！yy不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"tt\", \"ll\"]','rr',NULL,'2025-08-02','09:09:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 20:17:03','2025-08-01 19:35:44','2025-08-02 20:17:03',1,NULL,NULL,'{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:17:03',NULL,0,1,1,NULL,0,2,2),(192,1,NULL,NULL,94,'✅ 挖到宝了✨ww的tt也太香了吧！','✅ yy✅ \r\n路过被香味吸引进店，tt直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"rr\", \"ee\"]','tt',NULL,'2025-08-01','08:30:00','ready_to_publish','unpublished','approved','approved','normal',NULL,'2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,1,'2025-08-02 02:47:25','{\"at_users\": [\"@bb\"]}',1,'2025-08-02 12:56:29',1,0,1,1,NULL,0,2,2),(193,1,NULL,NULL,93,'tt必点单品！✅ yy一口就爱上✅ ','✅ ww✅ \r\n今天终于吃到tt的招牌yy，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"tt\", \"yy\"]','jj',NULL,'2025-08-01','08:45:00','pending_publish','pending_publish','approved','approved','normal',NULL,'2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,1,'2025-08-02 02:47:25','{\"at_users\": [\"@tt\"]}',1,'2025-08-02 12:58:15',1,0,1,1,'批量标记为失败',0,2,2),(194,1,NULL,NULL,98,'✅ 闺蜜下午茶首选?✅ yyww颜值味道双在线！','✅  yy✅ \r\n和姐妹约会的秘密基地！ww不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"bb\", \"tt\"]','ll',NULL,'2025-08-01','09:17:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,NULL,NULL,'{\"at_users\": [\"@ll\"]}',1,'2025-08-02 20:17:28',NULL,0,1,1,NULL,0,2,2),(195,1,NULL,NULL,102,'节日限定✅ ttww错过等一年！','✅ ee✅ 去\r\n圣诞季必吃的限定款ww！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"tt\", \"rr\"]','rr',NULL,'2025-08-01','09:23:00','published','published','approved','approved','high','2025-08-02 14:52:33','2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,1,'2025-08-02 02:47:26','{\"at_users\": [\"@tt\"]}',1,'2025-08-02 20:17:28',NULL,0,1,1,NULL,0,2,2),(196,1,NULL,NULL,100,'家庭聚餐就选这✅ eerr老少皆宜✅ ','✅ tt\r\n带全家来吃ee，✅ rr获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"ll\", \"ee\"]','ll',NULL,'2025-08-01','09:54:00','published','published','approved','approved','high','2025-08-02 17:32:58','2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,1,'2025-08-02 02:47:26','{\"at_users\": [\"@tt\"]}',1,'2025-08-02 20:17:28',NULL,0,1,1,NULL,0,2,2),(197,1,NULL,NULL,101,'减肥也能吃！✅ rr轻食系列yy太✅ 了','✅ ee\r\n健身教练推荐的yy，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"rr\", \"ll\"]','tt',NULL,'2025-08-02','08:30:00','pending_publish','pending_publish','approved','approved','normal',NULL,'2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,1,'2025-08-02 02:47:26','{\"at_users\": [\"@yy\"]}',1,'2025-08-02 20:17:28',NULL,0,1,1,NULL,0,2,2),(198,1,NULL,NULL,99,'✅ 打工人午餐救星！rree15分钟上菜⚡','✅ yy✅ \r\n工作日中午的快乐源泉～ee量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"yy\", \"ee\", \"ll\"]','jj',NULL,'2025-08-02','08:44:00','pending_publish','pending_publish','approved','approved','normal',NULL,'2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,1,'2025-08-02 02:47:26','{\"at_users\": [\"@yy\"]}',1,'2025-08-02 20:17:28',NULL,0,1,1,NULL,0,2,2),(199,1,NULL,NULL,97,'深夜放毒时间到！✅ rree太罪恶了✅ ','✅ ee\r\n半夜饿到不行发现rr还营业！✅ ee热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"jj\", \"ee\"]','ee',NULL,'2025-08-02','09:08:00','pending_publish','pending_publish','approved','approved','normal',NULL,'2025-08-02 20:17:28','2025-08-01 19:40:02','2025-08-02 20:17:28',1,1,'2025-08-02 02:47:26','{\"at_users\": [\"@rr\"]}',1,'2025-08-02 20:17:28',NULL,0,1,1,NULL,0,2,2),(201,1,NULL,NULL,102,'节日限定✅ ttww错过等一年！','✅ ee✅ 去\r\n圣诞季必吃的限定款ww！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食22','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"tt\", \"rr\"]','yy',NULL,'2025-08-01','08:55:00','published','published','approved','approved','high','2025-08-02 14:52:02','2025-08-02 20:16:40','2025-08-01 19:46:11','2025-08-02 20:16:40',1,1,'2025-08-02 02:06:48','{\"at_users\": []}',1,'2025-08-02 20:16:40',NULL,0,1,1,NULL,0,2,2),(205,1,NULL,NULL,100,'家庭聚餐就选这✅ rree老少皆宜✅ ','✅ ww\r\n带全家来吃rr，✅ ee获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"ee\", \"jj\"]','ee',NULL,'2025-08-02','08:30:00','pending_publish','pending_publish','approved','approved','low',NULL,'2025-08-02 20:16:40','2025-08-01 19:46:11','2025-08-02 20:16:40',1,1,'2025-08-01 23:47:18','{\"at_users\": [\"@jj\"]}',1,'2025-08-02 20:16:28',1,0,1,1,NULL,0,2,2),(206,1,NULL,NULL,98,'✅ 闺蜜下午茶首选?✅ yytt颜值味道双在线！','✅  rr✅ \r\n和姐妹约会的秘密基地！tt不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"ll\", \"tt\"]','yy',NULL,'2025-08-02','08:48:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 20:16:40','2025-08-01 19:46:11','2025-08-02 20:16:40',1,NULL,NULL,'{\"at_users\": [\"@yy\"]}',1,'2025-08-02 20:16:40',NULL,0,1,1,NULL,0,2,2),(216,1,35,54,99,'✅ 打工人午餐救星！rryy15分钟上菜⚡','✅ yy✅ \r\n工作日中午的快乐源泉～yy量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"tt\", \"ee\"]','jj',NULL,'2025-08-02','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:24:26','2025-08-02 22:24:26','2025-08-02 22:24:26',1,NULL,NULL,'{\"at_users\": [\"@jj\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(217,1,35,54,100,'家庭聚餐就选这✅ rree老少皆宜✅ ','✅ tt\r\n带全家来吃rr，✅ ee获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"tt\", \"ll\"]','yy',NULL,'2025-08-02','08:51:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:24:26','2025-08-02 22:24:26','2025-08-02 22:24:26',1,NULL,NULL,'{\"at_users\": [\"@ee\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(218,1,35,54,101,'减肥也能吃！✅ tt轻食系列ww太✅ 了','✅ rr\r\n健身教练推荐的ww，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"jj\", \"ll\"]','ee',NULL,'2025-08-02','09:09:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:24:26','2025-08-02 22:24:26','2025-08-02 22:24:26',1,NULL,NULL,'{\"at_users\": [\"@yy\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(219,1,35,54,94,'✅ 挖到宝了✨ww的yy也太香了吧！','✅ ww✅ \r\n路过被香味吸引进店，yy直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"jj\", \"yy\"]','ee',NULL,'2025-08-02','09:28:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:24:27','2025-08-02 22:24:27','2025-08-02 22:24:27',1,NULL,NULL,'{\"at_users\": [\"@rr\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(220,1,35,54,98,'✅ 闺蜜下午茶首选?✅ rrtt颜值味道双在线！','✅  yy✅ \r\n和姐妹约会的秘密基地！tt不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"bb\", \"ll\", \"yy\"]','ee',NULL,'2025-08-02','09:47:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 22:24:27','2025-08-02 22:24:27','2025-08-02 22:24:27',1,NULL,NULL,'{\"at_users\": [\"@jj\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(221,1,35,54,102,'节日限定✅ yyww错过等一年！','✅ rr✅ 去\r\n圣诞季必吃的限定款ww！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"yy\", \"bb\", \"tt\"]','yy',NULL,'2025-08-03','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:24:27','2025-08-02 22:24:27','2025-08-02 22:24:27',1,NULL,NULL,'{\"at_users\": [\"@jj\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(222,1,35,54,93,'yy必点单品！✅ tt一口就爱上✅ ','✅ rr✅ \r\n今天终于吃到yy的招牌tt，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"yy\", \"bb\"]','tt',NULL,'2025-08-03','08:41:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:24:27','2025-08-02 22:24:27','2025-08-02 22:24:27',1,NULL,NULL,'{\"at_users\": [\"@rr\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(223,1,35,54,97,'深夜放毒时间到！✅ wwtt太罪恶了✅ ','✅ ee\r\n半夜饿到不行发现ww还营业！✅ tt热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"ll\", \"ee\"]','yy',NULL,'2025-08-03','09:16:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:24:27','2025-08-02 22:24:27','2025-08-02 22:24:27',1,NULL,NULL,'{\"at_users\": [\"@jj\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(224,3,36,55,97,'深夜放毒时间到！✅ rrtt太罪恶了✅ ','✅ yy\r\n半夜饿到不行发现rr还营业！✅ tt热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ww\", \"tt\"]','ee',NULL,'2025-08-02','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"ll\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(225,3,36,55,101,'减肥也能吃！✅ tt轻食系列ee太✅ 了','✅ rr\r\n健身教练推荐的ee，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"yy\"]','yy',NULL,'2025-08-02','08:50:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"yy\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(226,3,36,55,93,'tt必点单品！✅ 我问问一口就爱上✅ ','✅ ww✅ \r\n今天终于吃到tt的招牌我问问，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ww\", \"bb\"]','tt',NULL,'2025-08-02','08:57:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"bb\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(227,3,36,55,98,'✅ 闺蜜下午茶首选?✅ 啊啊啊tt颜值味道双在线！','✅  ww✅ \r\n和姐妹约会的秘密基地！tt不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ee\", \"ll\"]','yy',NULL,'2025-08-02','09:18:00','draft','unpublished','pending','length_exceeded','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"tt\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(228,3,36,55,102,'节日限定✅ 啊啊啊ww错过等一年！','✅ yy✅ 去\r\n圣诞季必吃的限定款ww！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"ll\"]','ww',NULL,'2025-08-02','09:26:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"ww\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(229,3,36,55,94,'✅ 挖到宝了✨啊啊啊的ww也太香了吧！','✅ ee✅ \r\n路过被香味吸引进店，ww直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"yy\"]','ee',NULL,'2025-08-03','08:30:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"ll\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(230,3,36,55,99,'✅ 打工人午餐救星！yyww15分钟上菜⚡','✅ ee✅ \r\n工作日中午的快乐源泉～ww量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"rr\", \"jj\"]','ee',NULL,'2025-08-03','08:55:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"ee\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(231,3,36,55,100,'家庭聚餐就选这✅ yyrr老少皆宜✅ ','✅ ww\r\n带全家来吃yy，✅ rr获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐','[\"ww\", \"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"bb\"]','tt',NULL,'2025-08-03','09:18:00','draft','unpublished','pending','pending','normal',NULL,'2025-08-02 22:31:50','2025-08-02 22:31:50','2025-08-02 22:31:50',1,NULL,NULL,'{\"at_users\": [\"yy\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2);
/*!40000 ALTER TABLE `contents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_schedules`
--

DROP TABLE IF EXISTS `display_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `display_date` date NOT NULL,
  `display_time` time NOT NULL,
  `is_fixed_time` tinyint(1) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `display_order` int(11) DEFAULT NULL,
  `actual_display_time` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `client_id` (`client_id`),
  KEY `content_id` (`content_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_schedules`
--

LOCK TABLES `display_schedules` WRITE;
/*!40000 ALTER TABLE `display_schedules` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_schedules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_settings`
--

DROP TABLE IF EXISTS `display_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `order_type` varchar(20) DEFAULT NULL,
  `custom_order` text,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_id` (`client_id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_settings`
--

LOCK TABLES `display_settings` WRITE;
/*!40000 ALTER TABLE `display_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `menu_items`
--

DROP TABLE IF EXISTS `menu_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `menu_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `permission` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `order` int(11) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_menu_items_parent_id` (`parent_id`),
  KEY `idx_menu_items_order` (`order`),
  KEY `idx_menu_items_is_active` (`is_active`),
  CONSTRAINT `fk_menu_items_parent` FOREIGN KEY (`parent_id`) REFERENCES `menu_items` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `menu_items`
--

LOCK TABLES `menu_items` WRITE;
/*!40000 ALTER TABLE `menu_items` DISABLE KEYS */;
INSERT INTO `menu_items` VALUES (1,'控制台','/simple/dashboard','bi bi-speedometer2','dashboard.view',NULL,1,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(2,'模板管理','/simple/templates','bi bi-layer-group','template.manage',NULL,2,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(3,'客户管理','/simple/clients','bi bi-people','client.manage',NULL,3,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(4,'内容生成','/simple/content','bi bi-pencil-square','content.generate',NULL,4,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(5,'初审文案','/simple/review-content','bi bi-clipboard-check','content.review',NULL,5,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(6,'图片上传','/simple/image-upload','bi bi-image','image.upload',NULL,6,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(7,'最终审核','/simple/final-review','bi bi-check2-square','content.final_review',NULL,7,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(8,'客户审核','/simple/client-review','bi bi-person-check','client.review',NULL,8,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(9,'发布管理','/simple/publish-manage','bi bi-send','publish.manage',NULL,9,1,'2025-07-27 13:31:21','2025-07-27 13:31:21'),(10,'发布状态','/simple/publish-status-manage','bi bi-list-check','publish.status',NULL,10,1,'2025-07-27 13:31:21','2025-07-27 13:31:21'),(11,'用户管理','/simple/users','bi bi-person-gear','user.manage',NULL,11,1,'2025-07-27 13:31:21','2025-07-27 13:31:21'),(12,'系统设置','/simple/system','bi bi-gear','system.settings',NULL,12,1,'2025-07-27 13:31:21','2025-07-27 13:31:21');
/*!40000 ALTER TABLE `menu_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `type` varchar(30) NOT NULL,
  `related_content_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `recipient_id` int(11) NOT NULL,
  `priority` varchar(10) DEFAULT 'normal',
  PRIMARY KEY (`id`),
  KEY `related_content_id` (`related_content_id`),
  KEY `recipient_id` (`recipient_id`),
  KEY `ix_notifications_created_at` (`created_at`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`related_content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'user.view','查看用户'),(2,'user.manage','管理用户'),(3,'client.view','查看客户'),(4,'client.manage','管理客户'),(5,'template.view','查看模板'),(6,'template.manage','管理模板'),(7,'content.view','查看文案'),(8,'content.manage','管理文案'),(9,'review.first','初审管理'),(10,'image.view','查看图片'),(11,'image.manage','管理图片'),(12,'review.final','终审管理'),(13,'publish.view','查看发布'),(14,'publish.manage','管理发布'),(15,'system.settings','系统设置'),(16,'notification.view','查看通知'),(17,'notification.manage','管理通知'),(48,'dashboard_access','控制面板'),(49,'user_manage','用户管理'),(50,'client_manage','客户管理'),(51,'template_manage','模板管理'),(52,'content_manage','文案管理'),(53,'content_generate','生成文案'),(54,'topic_manage','话题管理'),(55,'task_manage','任务管理'),(56,'publish_manage','发布管理'),(57,'supplement_manage','文案补充'),(58,'display_manage','文案展示'),(59,'notification_manage','通知中心'),(60,'stats_view','数据统计'),(61,'export_manage','导入导出'),(62,'system_settings','系统设置'),(63,'image_manage','图片管理权限'),(64,'client_review_manage','客户审核管理权限'),(65,'publish_manage_new','发布管理权限（新版）'),(66,'dashboard.view','控制台查看'),(67,'content.generate','内容生成'),(68,'content.review','内容审核'),(69,'image.upload','图片上传'),(70,'content.final_review','最终审核'),(71,'client.review','客户审核'),(72,'publish.status','发布状态');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions_backup`
--

DROP TABLE IF EXISTS `permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions_backup` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions_backup`
--

LOCK TABLES `permissions_backup` WRITE;
/*!40000 ALTER TABLE `permissions_backup` DISABLE KEYS */;
INSERT INTO `permissions_backup` VALUES (1,'查看用户列表','查看系统用户列表'),(2,'新增用户','创建新的系统用户'),(3,'编辑用户','编辑系统用户信息'),(4,'删除用户','删除系统用户'),(5,'分配用户角色','为用户分配系统角色'),(6,'查看客户列表','查看所有客户'),(7,'新增客户','添加新客户'),(8,'编辑客户','编辑客户信息'),(9,'删除客户','删除客户'),(10,'创建客户分享链接','为客户创建分享链接'),(11,'管理客户审核权限','管理客户的审核权限'),(12,'查看模板列表','查看所有模板'),(13,'新增模板','创建新的模板'),(14,'编辑模板','编辑现有模板'),(15,'删除模板','删除模板'),(16,'管理模板分类','管理模板分类'),(17,'查看文案列表','查看所有文案'),(18,'新建文案任务','创建新的文案生成任务'),(19,'批量生成文案','批量生成文案内容'),(20,'编辑文案','编辑文案内容'),(21,'预览文案','预览文案效果'),(22,'删除文案','删除文案'),(23,'查看待初审文案','查看待初审的文案列表'),(24,'初审通过','将文案标记为初审通过'),(25,'初审编辑','在初审阶段编辑文案'),(26,'初审驳回','驳回初审文案'),(27,'批量初审操作','批量处理初审文案'),(28,'查看待上传图片文案','查看需要上传图片的文案'),(29,'上传图片','为文案上传图片'),(30,'编辑图片','编辑文案图片'),(31,'删除图片','删除文案图片'),(32,'提交图片审核','提交图片进入下一审核流程'),(33,'查看待终审文案','查看待终审的文案'),(34,'终审通过','将文案标记为终审通过'),(35,'终审编辑','在终审阶段编辑文案'),(36,'终审驳回','驳回终审文案'),(37,'批量终审操作','批量处理终审文案'),(38,'查看待发布文案','查看待发布的文案'),(39,'设置发布优先级','设置文案发布优先级'),(40,'查看发布状态','查看文案发布状态'),(41,'手动更新发布状态','手动更新文案发布状态'),(42,'发布失败处理','处理发布失败的文案'),(43,'查看系统设置','查看系统配置选项'),(44,'修改系统配置','修改系统配置选项'),(45,'管理审核流程','管理文案审核流程'),(46,'管理快捷理由','管理拒绝快捷理由'),(47,'查看系统日志','查看系统操作日志'),(48,'user.view','查看用户'),(49,'user.create','创建用户'),(50,'user.edit','编辑用户'),(51,'user.delete','删除用户'),(52,'user.manage','管理用户'),(53,'client.view','查看客户'),(54,'client.create','创建客户'),(55,'client.edit','编辑客户'),(56,'client.delete','删除客户'),(57,'client.manage','管理客户'),(58,'template.view','查看模板'),(59,'template.create','创建模板'),(60,'template.edit','编辑模板'),(61,'template.delete','删除模板'),(62,'template.manage','管理模板'),(63,'content.view','查看文案'),(64,'content.create','创建文案'),(65,'content.edit','编辑文案'),(66,'content.delete','删除文案'),(67,'content.manage','管理文案'),(68,'review.first.view','查看初审列表'),(69,'review.first.approve','初审通过'),(70,'review.first.reject','初审驳回'),(71,'review.first.manage','管理初审(包含所有初审相关权限)'),(72,'image.view','查看图片'),(73,'image.upload','上传图片'),(74,'image.edit','编辑图片'),(75,'image.delete','删除图片'),(76,'image.manage','管理图片'),(77,'review.final.view','查看终审列表'),(78,'review.final.approve','终审通过'),(79,'review.final.reject','终审驳回'),(80,'review.final.manage','管理终审(包含所有终审相关权限)'),(81,'publish.view','查看发布'),(82,'publish.approve','发布文案'),(83,'publish.manage','管理发布'),(84,'system.view','查看系统设置'),(85,'system.edit','编辑系统设置'),(86,'system.manage','管理系统(包含所有系统相关权限)'),(87,'review.first','初审管理'),(88,'review.final','终审管理'),(89,'system.settings','系统设置'),(90,'notification.view','查看通知'),(91,'notification.manage','管理通知');
/*!40000 ALTER TABLE `permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_records`
--

DROP TABLE IF EXISTS `publish_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `status` varchar(20) DEFAULT NULL,
  `platform` varchar(50) DEFAULT NULL,
  `account` varchar(100) DEFAULT NULL,
  `publish_url` varchar(255) DEFAULT NULL,
  `publish_time` datetime DEFAULT NULL,
  `error_message` text,
  `ext_info` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  CONSTRAINT `publish_records_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_records`
--

LOCK TABLES `publish_records` WRITE;
/*!40000 ALTER TABLE `publish_records` DISABLE KEYS */;
INSERT INTO `publish_records` VALUES (1,201,'success','小红书','ssss','http://xhslink.com/m/FXp1QxMnBn','2025-08-02 14:52:29','','{\"test_source\":\"api_test_page\",\"timestamp\":\"2025-08-02T06:52:28.596Z\"}','2025-08-02 14:52:29','2025-08-02 18:10:24'),(2,195,'success','小红书','dddd','http://xhslink.com/m/789viKxrUo1','2025-08-02 14:52:48','','{\"test_source\":\"api_test_page\",\"timestamp\":\"2025-08-02T06:52:48.085Z\"}','2025-08-02 14:52:48','2025-08-02 18:10:30'),(3,196,'success','小红书','xxxxx','http://xhslink.com/m/48JwCnZCRM4','2025-08-02 17:33:24','','{\"test_source\":\"api_test_page\",\"timestamp\":\"2025-08-02T09:33:23.974Z\"}','2025-08-02 17:33:24','2025-08-02 17:33:24');
/*!40000 ALTER TABLE `publish_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_timeouts`
--

DROP TABLE IF EXISTS `publish_timeouts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_timeouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timeout_minutes` int(11) DEFAULT NULL,
  `action` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_timeouts`
--

LOCK TABLES `publish_timeouts` WRITE;
/*!40000 ALTER TABLE `publish_timeouts` DISABLE KEYS */;
/*!40000 ALTER TABLE `publish_timeouts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quick_reasons`
--

DROP TABLE IF EXISTS `quick_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `quick_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` varchar(200) NOT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quick_reasons`
--

LOCK TABLES `quick_reasons` WRITE;
/*!40000 ALTER TABLE `quick_reasons` DISABLE KEYS */;
/*!40000 ALTER TABLE `quick_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rejection_reasons`
--

DROP TABLE IF EXISTS `rejection_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rejection_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `reason` text NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `is_client` tinyint(1) DEFAULT '0',
  `rejection_type` varchar(20) DEFAULT 'content' COMMENT '驳回类型：content=文案问题, image=图片问题, both=两者都有问题',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_rejection_reasons_type` (`rejection_type`),
  KEY `idx_rejection_reasons_content_type` (`content_id`,`rejection_type`),
  CONSTRAINT `rejection_reasons_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `rejection_reasons_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rejection_reasons`
--

LOCK TABLES `rejection_reasons` WRITE;
/*!40000 ALTER TABLE `rejection_reasons` DISABLE KEYS */;
INSERT INTO `rejection_reasons` VALUES (1,162,'整体效果不符合预期，需要重新制作','2025-08-01 19:25:17',1,0,'both'),(12,205,'内容与图片的配合度不够','2025-08-01 21:25:47',1,0,'both'),(13,192,'内容和视觉效果都需要提升','2025-08-01 21:26:00',1,0,'both'),(14,161,'整体质量不符合标准，需要全面优化','2025-08-01 23:14:11',NULL,1,'both'),(15,159,'缺少关键信息，需要补充完整\r\n文案与图片配合度不够，需要重新匹配','2025-08-01 23:17:02',NULL,1,'both'),(18,201,'品牌一致性问题，需要全面调整','2025-08-02 01:18:14',NULL,1,'both');
/*!40000 ALTER TABLE `rejection_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (32,3,7),(33,3,8),(35,7,7),(36,7,10),(37,7,11),(38,4,3),(39,4,4),(44,8,7),(45,8,12),(47,9,5),(48,9,6),(50,5,13),(51,5,14),(52,5,16),(53,5,17),(75,2,66),(76,2,70),(77,2,14),(78,2,69),(79,2,6),(80,2,15),(81,2,68),(82,2,72),(83,2,4),(84,2,2),(85,2,67),(86,2,71),(87,10,15),(88,10,66),(89,10,71),(90,10,2),(91,10,68),(92,10,70),(93,10,72),(94,10,14),(95,10,69),(96,10,4),(97,10,67),(98,10,6),(99,11,66),(100,11,67),(101,11,69),(102,11,68),(103,12,66),(104,12,71),(105,12,68),(106,12,70),(107,6,66);
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions_backup`
--

DROP TABLE IF EXISTS `role_permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_permissions_backup` (
  `id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions_backup`
--

LOCK TABLES `role_permissions_backup` WRITE;
/*!40000 ALTER TABLE `role_permissions_backup` DISABLE KEYS */;
INSERT INTO `role_permissions_backup` VALUES (186,2,1),(187,2,2),(188,2,3),(189,2,4),(190,2,5),(191,2,6),(192,2,7),(193,2,8),(194,2,9),(195,2,10),(196,2,11),(197,2,12),(198,2,13),(199,2,14),(200,2,15),(201,2,16),(202,2,17),(203,2,18),(204,2,19),(205,2,20),(206,2,21),(207,2,22),(208,2,23),(209,2,24),(210,2,25),(211,2,26),(212,2,27),(213,2,28),(214,2,29),(215,2,30),(216,2,31),(217,2,32),(218,2,33),(219,2,34),(220,2,35),(221,2,36),(222,2,37),(223,2,38),(224,2,39),(225,2,40),(226,2,41),(227,2,42),(228,2,43),(229,2,44),(230,2,45),(231,2,46),(232,2,47),(233,2,48),(234,2,49),(235,2,50),(236,2,51),(237,2,52),(238,2,53),(239,2,54),(240,2,55),(241,2,56),(242,2,57),(243,2,58),(244,2,59),(245,2,60),(246,2,61),(247,2,62),(248,2,63),(249,2,64),(250,2,65),(251,2,66),(252,2,67),(253,2,68),(254,2,69),(255,2,70),(256,2,71),(257,2,72),(258,2,73),(259,2,74),(260,2,75),(261,2,76),(262,2,77),(263,2,78),(264,2,79),(265,2,80),(266,2,81),(267,2,82),(268,2,83),(269,2,84),(270,2,85),(271,2,86),(272,2,87),(273,2,88),(274,2,89),(275,2,90),(276,2,91),(277,3,63),(278,3,67),(279,7,63),(280,7,72),(281,7,76),(282,4,53),(283,4,57),(284,6,63),(285,6,58),(286,8,63),(287,8,88),(288,9,58),(289,9,62),(290,5,81),(291,5,83),(292,5,90),(293,5,91);
/*!40000 ALTER TABLE `role_permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (2,'超级管理员','系统超级管理员，拥有所有权限','2025-07-13 02:49:12'),(3,'内容编辑','负责编辑文案内容','2025-07-13 10:39:29'),(4,'客户经理','负责管理客户','2025-07-13 10:39:29'),(5,'运营专员','负责内容发布和运营','2025-07-13 10:39:29'),(6,'普通用户','普通系统用户','2025-07-13 10:39:29'),(7,'图文编辑','负责管理图片和文案','2025-07-15 15:55:41'),(8,'最终审核员','负责内容终审','2025-07-15 15:55:41'),(9,'模板管理员','负责管理模板','2025-07-15 15:59:04'),(10,'管理员','拥有大部分管理权限','2025-07-27 13:31:21'),(11,'编辑','拥有内容编辑权限','2025-07-27 13:31:21'),(12,'审核员','拥有内容审核权限','2025-07-27 13:31:21');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_settings`
--

DROP TABLE IF EXISTS `system_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL,
  `value` text,
  `description` varchar(200) DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_settings`
--

LOCK TABLES `system_settings` WRITE;
/*!40000 ALTER TABLE `system_settings` DISABLE KEYS */;
INSERT INTO `system_settings` VALUES (3,'auto_publish_enabled','1','是否启用自动发布','2025-07-29 23:22:47',1),(11,'ENABLE_FIRST_REVIEW','1','是否启用初审功能','2025-07-28 20:58:32',1),(12,'ENABLE_FINAL_REVIEW','1','是否启用最终审核功能','2025-07-28 21:37:11',1),(13,'IMAGE_UPLOAD_MAX_SIZE','10485760','图片上传最大大小（字节）','2025-07-28 17:59:16',1),(14,'IMAGE_UPLOAD_ALLOWED_TYPES','jpg,jpeg,png,gif,webp','允许上传的图片类型','2025-07-28 17:59:16',1),(16,'MAX_IMAGES_PER_CONTENT','9','每篇文案最大图片数量','2025-07-28 17:59:16',1),(17,'API_KEY','fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW','API访问密钥','2025-07-28 17:59:16',1),(18,'PUBLISH_TIMEOUT','120','发布超时时间（秒）- 测试设置为2分钟','2025-07-28 17:59:16',1),(19,'PUBLISH_TIMEOUT_ACTION','keep_timeout','超时处理策略：保持超时状态','2025-07-28 17:59:23',1),(20,'PUBLISH_MAX_RETRY_COUNT','3','发布最大重试次数','2025-07-28 17:59:16',1);
/*!40000 ALTER TABLE `system_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` varchar(20) DEFAULT 'processing',
  `target_count` int(11) DEFAULT '0',
  `actual_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前任务中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `tasks_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `tasks_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tasks`
--

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;
INSERT INTO `tasks` VALUES (35,1,'2025年08月02日任务',NULL,'processing',0,0,'2025-08-02 22:24:26','2025-08-02 22:24:26',1,0),(36,3,'2025年08月02日任务',NULL,'processing',0,0,'2025-08-02 22:31:50','2025-08-02 22:31:50',1,0);
/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_categories`
--

DROP TABLE IF EXISTS `template_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `template_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `template_categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_categories`
--

LOCK TABLES `template_categories` WRITE;
/*!40000 ALTER TABLE `template_categories` DISABLE KEYS */;
INSERT INTO `template_categories` VALUES (1,'美妆护肤',NULL,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'时尚穿搭',NULL,2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'美食探店',NULL,3,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'旅游攻略',NULL,4,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'生活分享',NULL,5,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'护肤心得',1,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'彩妆教程',1,2,'2025-07-13 10:39:29','2025-07-17 02:38:25'),(8,'穿搭搭配',2,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'潮流趋势',2,2,'2025-07-13 10:39:29','2025-07-19 01:25:22'),(10,'餐厅推荐',3,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(11,'美食制作',3,2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `template_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_marks`
--

DROP TABLE IF EXISTS `template_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `type` varchar(20) DEFAULT 'text',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_marks`
--

LOCK TABLES `template_marks` WRITE;
/*!40000 ALTER TABLE `template_marks` DISABLE KEYS */;
INSERT INTO `template_marks` VALUES (1,'品牌名称','','text','2025-07-13 17:32:29'),(2,'店铺地址','','text','2025-07-13 17:33:24'),(4,'标记2','','text','2025-07-13 17:34:01'),(5,'标记3','','text','2025-07-13 17:34:05'),(7,'标记5','','text','2025-07-13 17:34:13'),(8,'商品名称','','text','2025-07-13 18:24:44'),(9,'标记6','','text','2025-07-17 02:45:13'),(11,'标记8','11','text','2025-07-17 02:59:22');
/*!40000 ALTER TABLE `template_marks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `templates`
--

DROP TABLE IF EXISTS `templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `creator_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  `marks` json DEFAULT NULL COMMENT '模板中的标记列表，JSON格式存储',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `creator_id` (`creator_id`),
  CONSTRAINT `templates_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `template_categories` (`id`),
  CONSTRAINT `templates_ibfk_2` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `templates`
--

LOCK TABLES `templates` WRITE;
/*!40000 ALTER TABLE `templates` DISABLE KEYS */;
INSERT INTO `templates` VALUES (93,11,'{品牌名称}必点单品！✅ {商品名称}一口就爱上✅ ','✅ {店铺地址}✅ \r\n今天终于吃到{品牌名称}的招牌{商品名称}，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常',1,'2025-07-20 17:54:35','2025-07-27 01:20:44',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(94,11,'✅ 挖到宝了✨{品牌名称}的{商品名称}也太香了吧！','✅ {店铺地址}✅ \r\n路过被香味吸引进店，{商品名称}直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店',1,'2025-07-20 17:54:53','2025-07-27 01:20:32',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(95,1,'本地人才知道的隐藏美味！✅ {品牌名称}{商品名称}✅ ','✅ {店铺地址}✅ \r\n同事强烈安利的{商品名称}，果然没让我失望！独家秘制配方，吃完还想打包十份带走！ #地方美食 #必吃清单',1,'2025-07-20 17:55:05','2025-08-02 19:30:35',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(96,1,'早餐新选择！✅ {品牌名称}{商品名称}开启元气一天☀️','✅ {店铺地址}\r\n早起就是为了这口{商品名称}！现点现做，酥脆爆浆，搭配咖啡绝绝子～打工人早餐首选 #早餐推荐 #美食日记',1,'2025-07-20 17:55:18','2025-08-02 19:30:35',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(97,11,'深夜放毒时间到！✅ {品牌名称}{商品名称}太罪恶了✅ ','✅ {店铺地址}\r\n半夜饿到不行发现{品牌名称}还营业！✅ {商品名称}热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂',1,'2025-07-20 17:55:36','2025-07-27 01:21:32',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(98,11,'✅ 闺蜜下午茶首选?✅ {品牌名称}{商品名称}颜值味道双在线！','✅  {店铺地址}✅ \r\n和姐妹约会的秘密基地！{商品名称}不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会',1,'2025-07-20 17:55:56','2025-07-27 01:20:53',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(99,11,'✅ 打工人午餐救星！{品牌名称}{商品名称}15分钟上菜⚡','✅ {店铺地址}✅ \r\n工作日中午的快乐源泉～{商品名称}量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食',1,'2025-07-20 17:56:12','2025-07-27 01:22:08',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(100,11,'家庭聚餐就选这✅ {品牌名称}{商品名称}老少皆宜✅ ','✅ {店铺地址}\r\n带全家来吃{品牌名称}，✅ {商品名称}获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐',1,'2025-07-20 17:56:32','2025-07-27 01:21:45',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(101,11,'减肥也能吃！✅ {品牌名称}轻食系列{商品名称}太✅ 了','✅ {店铺地址}\r\n健身教练推荐的{商品名称}，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食',1,'2025-07-20 17:56:47','2025-07-27 01:21:56',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(102,11,'节日限定✅ {品牌名称}{商品名称}错过等一年！','✅ {店铺地址}✅ 去\r\n圣诞季必吃的限定款{商品名称}！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食',1,'2025-07-20 17:57:00','2025-07-28 19:44:57',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(103,1,'撒大声地{店铺地址}防守小程序{标记3}','森岛帆高能更好的{店铺地址}{标记3}',1,'2025-08-02 19:29:33','2025-08-02 19:30:35',1,'[\"店铺地址\", \"标记3\"]');
/*!40000 ALTER TABLE `templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic_relations`
--

DROP TABLE IF EXISTS `topic_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topic_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic_id` int(11) NOT NULL,
  `related_topic_id` int(11) NOT NULL,
  `weight` int(11) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `topic_id` (`topic_id`),
  KEY `related_topic_id` (`related_topic_id`),
  CONSTRAINT `topic_relations_ibfk_1` FOREIGN KEY (`topic_id`) REFERENCES `topics` (`id`),
  CONSTRAINT `topic_relations_ibfk_2` FOREIGN KEY (`related_topic_id`) REFERENCES `topics` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic_relations`
--

LOCK TABLES `topic_relations` WRITE;
/*!40000 ALTER TABLE `topic_relations` DISABLE KEYS */;
/*!40000 ALTER TABLE `topic_relations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topics`
--

DROP TABLE IF EXISTS `topics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) DEFAULT 'random',
  `priority` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topics`
--

LOCK TABLES `topics` WRITE;
/*!40000 ALTER TABLE `topics` DISABLE KEYS */;
INSERT INTO `topics` VALUES (1,'#美妆分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'#护肤心得','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'#穿搭搭配','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'#美食探店','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'#旅游攻略','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'#生活分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'#好物推荐','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(8,'#购物分享','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'#职场穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(10,'#约会穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `topics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_menu_permissions`
--

DROP TABLE IF EXISTS `user_menu_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_menu_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_menu` (`user_id`,`menu_id`),
  KEY `menu_id` (`menu_id`),
  CONSTRAINT `user_menu_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_menu_permissions_ibfk_2` FOREIGN KEY (`menu_id`) REFERENCES `menu_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_menu_permissions`
--

LOCK TABLES `user_menu_permissions` WRITE;
/*!40000 ALTER TABLE `user_menu_permissions` DISABLE KEYS */;
INSERT INTO `user_menu_permissions` VALUES (9,1,1),(12,1,2),(14,1,3),(4,1,4),(6,1,5),(8,1,6),(11,1,7),(13,1,8),(3,1,9),(5,1,10),(7,1,11),(10,1,12),(19,2,1),(15,2,5),(55,2,6),(18,3,1),(46,3,4),(17,3,6),(20,4,1),(47,4,4),(21,4,7),(48,6,4),(23,6,9),(22,6,10),(2,7,1),(42,7,2),(34,7,3),(35,7,4),(36,7,5),(37,7,6),(38,7,7),(39,7,8),(40,7,9),(41,7,10),(43,7,11),(44,7,12),(52,13,1),(51,13,5),(53,14,1),(54,14,6);
/*!40000 ALTER TABLE `user_menu_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_permissions`
--

DROP TABLE IF EXISTS `user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_permissions`
--

LOCK TABLES `user_permissions` WRITE;
/*!40000 ALTER TABLE `user_permissions` DISABLE KEYS */;
INSERT INTO `user_permissions` VALUES (1,1,48),(2,1,49),(3,1,50),(4,1,51),(5,1,52),(6,1,53),(7,1,54),(8,1,55),(9,1,56),(10,1,57),(11,1,58),(12,1,59),(13,1,60),(14,1,61),(15,1,62),(16,7,51),(17,7,51),(18,7,53),(19,7,53),(20,7,14),(21,7,48),(22,7,50),(23,7,52),(24,7,63),(25,7,12),(26,7,62),(27,2,68),(28,4,68),(29,3,69);
/*!40000 ALTER TABLE `user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_permissions_backup`
--

DROP TABLE IF EXISTS `user_permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_permissions_backup` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_permissions_backup`
--

LOCK TABLES `user_permissions_backup` WRITE;
/*!40000 ALTER TABLE `user_permissions_backup` DISABLE KEYS */;
INSERT INTO `user_permissions_backup` VALUES (12,7,46),(13,7,29),(14,7,42),(15,7,54),(16,7,47),(17,7,58),(18,7,27),(19,7,53),(20,7,57),(21,7,30),(22,7,48);
/*!40000 ALTER TABLE `user_permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_role`
--

DROP TABLE IF EXISTS `user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_role`
--

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1,2),(2,1,2),(3,2,3),(4,3,7),(5,4,8),(6,5,6),(7,6,5);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_users_username` (`username`),
  UNIQUE KEY `ix_users_email` (`email`),
  KEY `ix_users_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','scrypt:32768:8:1$0rmBDS5oEZd2bcdq$d725703312dd5d810b1f994c784f4e49505dca32601dc18497f588431364d48814f9907c5d219e0311b59a5313c219e86ac68bb1ad98f2913827fa693c3bfed3','<EMAIL>','管理员','','2025-07-13 02:49:12','2025-08-02 12:20:11',1),(2,'reviewer','scrypt:32768:8:1$NCpOQWmmiMGT3Pd2$4a8a6467bccc9ef82688931534a570431bcc68ccde28f1c4f0cfd5a1af0599951d675335d8e666a47b222be0c262254e8ab6098711aca80cacf463972237da92','<EMAIL>','审核张三','','2025-07-15 15:55:41','2025-08-02 22:25:13',1),(3,'editor','scrypt:32768:8:1$VpmDj9Os7VVM2S2D$048767c53a123a50276547ada3082b6a1a433651e304a118b97072cf1d5984aeb31bd42cb98b7c581e6b90fe3ad0495a23cdbedb8ae33e55d3cedc8a5275d89e','<EMAIL>','图文编辑','','2025-07-15 15:55:41','2025-07-30 17:56:48',1),(4,'final_reviewer','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','最终审核员','','2025-07-15 15:55:41',NULL,1),(5,'client','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','客户用户',NULL,'2025-07-15 15:55:41',NULL,1),(6,'publisher','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','发布管理员','','2025-07-15 15:55:41',NULL,1),(7,'template_manager','scrypt:32768:8:1$EiLHEhiwakCKqJg1$8de1a3451c6fdbcaa1993d83b32d4cf0cb084ce0f727c3c6aeb3ea14204b90bf1aba6f370d069347b1f84c405f6a531c460262f23a32fd2e6a9aa2267fc773e1','<EMAIL>','模板管理员','','2025-07-15 15:59:04','2025-07-28 18:38:45',1),(13,'reviewer2','scrypt:32768:8:1$j4qYfmhKQl94ueJO$321c7d2e97a60d377b0b673a0069632824c300b0a9697896e895ca079aa0b799bc7c0ff7e4f58a19522b8858eafe0e848b41e78297605dd5be3746fd3d95ec7f',NULL,'审核李四','','2025-07-30 16:11:03','2025-07-30 16:48:04',1),(14,'editor2','scrypt:32768:8:1$8vbFe87LTCOjX4a5$38c2c6d0e402e2c9d1f3a351ae48148fdacd8b8609508ea38a70eea9213b0deaa388106e39ced697039eb367fa83c625754208be57335e8b6530c4b2573f04e9',NULL,'图片赵六','','2025-07-30 17:55:28','2025-07-30 18:02:02',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-02 22:32:47
