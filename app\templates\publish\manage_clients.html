<!-- 发布管理客户列表页面 -->
<style>
/* 排序按钮样式 */
.sort-btn {
    transition: all 0.2s ease;
    border-radius: 20px;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}
.sort-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.sort-btn.active {
    background-color: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

/* 搜索框样式 */
.input-group-sm .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
}
.input-group-sm .input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}
#searchInput:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="bi bi-send"></i> 发布管理</h2>
            <p class="text-muted">选择客户查看需要发布的文案</p>
        </div>
    </div>

    <!-- 客户列表 -->
    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="card-title mb-0">客户列表</h6>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center gap-2">
                        <!-- 排序说明 -->
                        <small class="text-muted me-1">排序:</small>
                        
                        <!-- 排序按钮 -->
                        <button type="button" class="btn btn-outline-primary btn-sm sort-btn {% if sort_by == 'desc' %}active{% endif %}" data-sort="desc">
                            <i class="bi bi-sort-numeric-down"></i> 多到少
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm sort-btn {% if sort_by == 'asc' %}active{% endif %}" data-sort="asc">
                            <i class="bi bi-sort-numeric-up"></i> 少到多
                        </button>
                        
                        <!-- 搜索框 -->
                        <form method="GET" action="{{ url_for('main_simple.publish_manage') }}" class="d-flex" id="searchForm">
                            <input type="hidden" name="sort_by" value="{{ sort_by }}" id="sortByInput">
                            <div class="input-group input-group-sm" style="width: 200px;">
                                <input type="text" class="form-control" name="search" value="{{ search }}" 
                                       placeholder="搜索客户名称..." id="searchInput">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if clients_with_pending %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>客户名称</th>
                                <th>待发布文案数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients_with_pending %}
                            <tr>
                                <td>
                                    <strong>{{ client.name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary text-white">
                                        {{ client.pending_count }} 篇待发布
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('main_simple.publish_manage_client_detail', client_id=client.id) }}"
                                       class="btn btn-primary btn-sm">
                                        <i class="bi bi-send"></i> 进入发布管理
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-send text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无待发布的文案</h4>
                    <p class="text-muted">所有文案都已完成发布或无需发布</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// 页面加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 绑定排序按钮事件
    document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');

            // 更新按钮状态
            document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // 更新隐藏字段
            document.getElementById('sortByInput').value = sortBy;

            // 提交表单
            document.getElementById('searchForm').submit();
        });
    });

    console.log('发布管理客户列表页面已加载');
});
</script>
</div>
