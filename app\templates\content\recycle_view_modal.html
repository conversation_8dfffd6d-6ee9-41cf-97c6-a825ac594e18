<!-- 回收站文案预览模态框内容 -->
<style>
.recycle-modal .content-area {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.recycle-modal .content-title {
    color: #495057;
    font-weight: 600;
    font-size: 22px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.recycle-modal .content-text {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
    line-height: 1.8;
    font-size: 15px;
    color: #495057;
    white-space: pre-line;
    word-wrap: break-word;
    text-align: left;
    margin-bottom: 25px;
}

.recycle-modal .right-panel {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e9ecef;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.recycle-modal .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recycle-modal .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.recycle-modal .badge {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 20px;
}

.recycle-modal .location-info {
    color: #dc3545;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recycle-modal .user-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.recycle-modal .info-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.recycle-modal .info-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.recycle-modal .basic-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.recycle-modal .status-section {
    margin-bottom: 20px;
}

.recycle-modal .status-item {
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.recycle-modal .status-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* 图片预览样式 */
.recycle-modal .image-grid-right {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.recycle-modal .image-item-right {
    position: relative;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1;
    cursor: pointer;
    transition: all 0.2s ease;
}

.recycle-modal .image-item-right:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.recycle-modal .image-thumbnail-right {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recycle-modal .image-order-right {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.recycle-modal .deleted-badge {
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}
</style>

<div class="recycle-modal">
<div class="row">
    <!-- 左侧：标题 + 内容 -->
    <div class="col-md-8">
        <div class="content-area">
            <!-- 文案标题 -->
            <h4 class="content-title">
                <i class="bi bi-file-text text-danger"></i>
                {{ content.title }}
                <span class="deleted-badge">已删除</span>
            </h4>

            <!-- 文案内容 -->
            <div class="content-text">{{ content.content|trim }}</div>
        </div>
    </div>

    <!-- 右侧：图片 + 话题标签 + @用户 + 位置信息 + 状态 -->
    <div class="col-md-4">
        <div class="right-panel">
            <!-- 配图区域 -->
            {% if images %}
            <div class="images-section">
                <h6 class="section-title">
                    <i class="bi bi-images text-success"></i>
                    配图预览 ({{ images|length }} 张)
                </h6>
                <div class="image-grid-right">
                    {% for image in images %}
                    <div class="image-item-right"
                         onclick="showImageModal('{{ url_for('static', filename='uploads/' + image.image_path) }}', '{{ image.original_name }}')"
                         style="cursor: pointer;">
                        <img src="{{ url_for('static', filename='uploads/' + image.thumbnail_path) }}"
                             class="image-thumbnail-right"
                             title="{{ image.original_name }}"
                             alt="{{ image.original_name }}">
                        <div class="image-order-right">{{ loop.index }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- 话题标签 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-tags text-primary"></i>
                    话题标签
                </h6>
                <div class="tags-container">
                    {% if content.topics_list %}
                        {% for topic in content.topics_list %}
                            <span class="badge bg-primary">#{{ topic }}</span>
                        {% endfor %}
                    {% else %}
                        <span class="text-muted">暂无话题标签</span>
                    {% endif %}
                </div>
            </div>

            <!-- @用户信息 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-person text-info"></i>
                    @用户
                </h6>
                <div class="user-info">
                    {% if content.at_users_list %}
                        <div class="tags-container">
                            {% for user in content.at_users_list %}
                                <span class="badge bg-info">@{{ user }}</span>
                            {% endfor %}
                        </div>
                    {% else %}
                        <span class="text-muted">暂无@用户</span>
                    {% endif %}
                </div>
            </div>

            <!-- 位置信息 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-geo-alt text-danger"></i>
                    位置信息
                </h6>
                <div class="location-info">
                    <i class="bi bi-geo-alt-fill"></i>
                    <span>{{ content.location if content.location else '暂无位置信息' }}</span>
                </div>
            </div>

            <!-- 客户和创建时间信息 -->
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-info-circle text-secondary"></i>
                    基本信息
                </h6>
                <div class="basic-info">
                    <div class="mb-2">
                        <span class="badge bg-primary">{{ content.client.name if content.client else '未知客户' }}</span>
                    </div>
                    <div>
                        <span class="text-muted small">
                            创建时间：{{ content.created_at.strftime('%Y-%m-%d %H:%M') if content.created_at else '未知时间' }}
                        </span>
                    </div>
                    {% if content.deleted_at %}
                    <div>
                        <span class="text-danger small">
                            删除时间：{{ content.deleted_at.strftime('%Y-%m-%d %H:%M') }}
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 发布记录 -->
            {% if publish_records %}
            <div class="info-section">
                <h6 class="section-title">
                    <i class="bi bi-broadcast text-success"></i>
                    发布记录
                </h6>
                {% for record in publish_records %}
                <div class="mb-3 p-2 border rounded">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-1">
                                <strong>平台：</strong>
                                <span class="badge bg-primary">{{ record.platform or '未知平台' }}</span>
                            </div>
                            {% if record.account %}
                            <div class="mb-1">
                                <strong>账号：</strong>
                                <span class="text-muted">{{ record.account }}</span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <div class="mb-1">
                                <strong>状态：</strong>
                                {% if record.status == 'success' %}
                                    <span class="badge bg-success">发布成功</span>
                                {% elif record.status == 'failed' %}
                                    <span class="badge bg-danger">发布失败</span>
                                {% else %}
                                    <span class="badge bg-warning">待发布</span>
                                {% endif %}
                            </div>
                            {% if record.publish_time %}
                            <div class="mb-1">
                                <strong>时间：</strong>
                                <small class="text-muted">{{ record.publish_time.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% if record.publish_url %}
                    <div class="mt-2">
                        <strong>链接：</strong>
                        <button type="button"
                                class="btn btn-primary btn-sm ms-1"
                                onclick="window.open('{{ record.publish_url }}', '_blank')"
                                oncontextmenu="copyPublishUrl(event, '{{ record.publish_url }}')"
                                title="左键打开发布链接，右键复制链接">
                            <i class="bi bi-link-45deg"></i> 发布链接
                        </button>
                    </div>
                    {% endif %}
                    {% if record.error_message %}
                    <div class="mt-2">
                        <strong>消息：</strong>
                        <small class="text-muted">{{ record.error_message }}</small>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- 状态信息 -->
            <div class="status-section">
                <h6 class="section-title">
                    <i class="bi bi-info-circle text-danger"></i>
                    状态信息
                </h6>
                <div class="status-item">
                    <strong>当前状态：</strong>
                    <span class="badge bg-danger">已删除</span>
                </div>
                <div class="status-item">
                    <strong>删除前状态：</strong>
                    {% if content.workflow_status == 'draft' %}
                        <span class="badge bg-secondary">草稿</span>
                    {% elif content.workflow_status == 'pending_review' %}
                        <span class="badge bg-warning">待初审</span>
                    {% elif content.workflow_status == 'first_reviewed' %}
                        <span class="badge bg-success">初审通过</span>
                    {% elif content.workflow_status == 'pending_image' %}
                        <span class="badge bg-info">待上传图片</span>
                    {% elif content.workflow_status == 'image_uploaded' %}
                        <span class="badge bg-info">图片已上传</span>
                    {% elif content.workflow_status == 'pending_final_review' %}
                        <span class="badge bg-warning">待最终审核</span>
                    {% elif content.workflow_status == 'final_review' %}
                        <span class="badge bg-warning">最终审核</span>
                    {% elif content.workflow_status == 'pending_client_review' %>
                        <span class="badge bg-primary">客户待审核</span>
                    {% elif content.workflow_status == 'client_approved' %}
                        <span class="badge bg-success">客户已通过</span>
                    {% elif content.workflow_status == 'client_rejected' %}
                        <span class="badge bg-danger">客户已拒绝</span>
                    {% elif content.workflow_status == 'ready_to_publish' %}
                        <span class="badge bg-info">待发布</span>
                    {% elif content.workflow_status == 'pending_publish' %}
                        <span class="badge bg-info">待发布</span>
                    {% elif content.workflow_status == 'publishing' %}
                        <span class="badge bg-primary">发布中</span>
                    {% elif content.workflow_status == 'published' %}
                        <span class="badge bg-success">已发布</span>
                    {% elif content.workflow_status == 'publish_failed' %>
                        <span class="badge bg-danger">发布失败</span>
                    {% elif content.workflow_status == 'publish_timeout' %}
                        <span class="badge bg-warning">发布超时</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ content.workflow_status }}</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<script>
// 回收站预览模态框已加载
console.log('回收站预览模态框已加载');

// 复制发布链接
function copyPublishUrl(event, url) {
    event.preventDefault(); // 阻止右键菜单

    // 使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(url).then(() => {
            showToast('链接已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(url);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(url);
    }
}

// 降级复制方案
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showToast('链接已复制到剪贴板', 'success');
        } else {
            showToast('复制失败，请手动复制', 'error');
        }
    } catch (err) {
        console.error('降级复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    }

    document.body.removeChild(textArea);
}

// Toast提示函数（如果不存在的话）
if (typeof showToast === 'undefined') {
    function showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
            </div>
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}
</script>
