<!-- 简化版客户管理页面内容 -->
<style>
/* 操作和排序控制栏样式优化 */
.control-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.control-section {
    padding: 0.75rem 0;
}

.control-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0;
}

/* 排序按钮组样式 */
.sort-btn-group .btn {
    transition: all 0.2s ease;
    border-radius: 4px;
    font-size: 0.875rem;
}

.sort-btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sort-btn-group .btn.active {
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .control-section {
        text-align: center;
    }

    .sort-btn-group {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .sort-btn-group .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2>客户管理</h2>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
        <form id="filterForm" method="GET">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="status" class="form-label">状态筛选</label>
                        <select class="form-select" id="status" name="status" onchange="filterClients()">
                            <option value="">全部状态</option>
                            <option value="1" {% if current_status == '1' or (current_status is none and not request.args.get('status')) %}selected{% endif %}>启用</option>
                            <option value="0" {% if current_status == '0' %}selected{% endif %}>禁用</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ current_search or '' }}"
                               placeholder="客户名称或联系人"
                               onkeypress="if(event.key==='Enter') filterClients()">
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <div class="mb-3 w-100">
                        <button type="button" class="btn btn-primary me-2" onclick="filterClients()">
                            <i class="bi bi-search"></i> 筛选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetFilter()">
                            <i class="bi bi-x-circle"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- 操作和排序控制栏 -->
    <div class="control-bar mb-3">
        <div class="px-3 py-2">
            <div class="row align-items-center">
                <!-- 操作列 -->
                <div class="col-md-6 col-12 mb-2 mb-md-0">
                    <div class="control-section d-flex align-items-center">
                        <span class="control-label me-3">操作：</span>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addClientModal">
                            <i class="bi bi-plus-lg"></i> 添加客户
                        </button>
                    </div>
                </div>
                <!-- 排序方式列 -->
                <div class="col-md-6 col-12">
                    <div class="control-section d-flex align-items-center justify-content-md-end justify-content-start">
                        <span class="control-label me-3">排序方式：</span>
                        <div class="btn-group btn-group-sm sort-btn-group" role="group">
                            <button type="button" class="btn btn-outline-info sort-btn {% if request.args.get('sort_by') == 'content_count_asc' %}active{% endif %}"
                                    data-sort="content_count_asc" title="文章数正序(少到多)">
                                <i class="bi bi-file-text"></i> 文章数 <i class="bi bi-sort-up"></i>
                            </button>
                            <button type="button" class="btn btn-outline-info sort-btn {% if request.args.get('sort_by') == 'content_count_desc' or not request.args.get('sort_by') %}active{% endif %}"
                                    data-sort="content_count_desc" title="文章数倒序(多到少)">
                                <i class="bi bi-file-text"></i> 文章数 <i class="bi bi-sort-down"></i>
                            </button>
                            <button type="button" class="btn btn-outline-warning sort-btn {% if request.args.get('sort_by') == 'last_content_asc' %}active{% endif %}"
                                    data-sort="last_content_asc" title="最后生成文章时间正序(早到晚)">
                                <i class="bi bi-clock"></i> 生成时间 <i class="bi bi-sort-up"></i>
                            </button>
                            <button type="button" class="btn btn-outline-warning sort-btn {% if request.args.get('sort_by') == 'last_content_desc' %}active{% endif %}"
                                    data-sort="last_content_desc" title="最后生成文章时间倒序(晚到早)">
                                <i class="bi bi-clock"></i> 生成时间 <i class="bi bi-sort-down"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 客户列表 -->
    <div class="card" id="client-list-container">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>客户名称</th>
                            <th>联系人</th>
                            <th>需要审核</th>
                            <th>文章数</th>
                            <th>发布数</th>
                            <th>标记数</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>最后生成文章时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                        <tr>
                            <td>{{ client.id }}</td>
                            <td>{{ client.name }}</td>
                            <td>{{ client.contact or '-' }}</td>
                            <td>
                                <span class="badge bg-{{ 'primary' if client.need_review else 'secondary' }} review-badge"
                                      style="cursor: pointer;"
                                      onclick="toggleClientReview({{ client.id }})"
                                      title="点击切换审核状态">
                                    {{ '需要审核' if client.need_review else '无需审核' }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'info' if client.total_content_count > 0 else 'light' }} text-{{ 'white' if client.total_content_count > 0 else 'dark' }}"
                                      title="文章总数（包括所有状态）">
                                    {{ client.total_content_count }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if client.published_count > 0 else 'light' }} text-{{ 'white' if client.published_count > 0 else 'dark' }}"
                                      title="已发布的文章数量">
                                    {{ client.published_count }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'warning' if client.mark_count > 0 else 'light' }} text-{{ 'dark' if client.mark_count > 0 else 'dark' }}"
                                      title="客户设置的标记数量">
                                    {{ client.mark_count or 0 }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if client.status else 'secondary' }} status-badge"
                                      style="cursor: pointer;"
                                      onclick="toggleClientStatus({{ client.id }})"
                                      title="点击切换状态">
                                    {{ '启用' if client.status else '禁用' }}
                                </span>
                            </td>
                            <td>{{ client.created_at.strftime('%Y-%m-%d') if client.created_at else '-' }}</td>
                            <td>
                                {% if client.last_content_time %}
                                    <span class="text-success" title="最后生成文章时间">
                                        {{ client.last_content_time.strftime('%Y-%m-%d') }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group" aria-label="客户操作">
                                    <button type="button" class="btn btn-outline-warning btn-sm"
                                            onclick="openMarkManager({{ client.id }}, '{{ client.name }}')"
                                            title="标记管理">
                                        <i class="bi bi-tags"></i> 标记管理
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm"
                                            onclick="viewClientDetail({{ client.id }}, '{{ client.name }}')"
                                            title="查看客户详细信息">
                                        <i class="bi bi-info-circle"></i> 详细信息
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                            onclick="editClient({{ client.id }})"
                                            title="编辑客户">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            onclick="deleteClient({{ client.id }}, '{{ client.name }}')"
                                            title="删除客户">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="10" class="text-center text-muted">暂无客户数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            {% if pagination %}
                {% include 'components/pagination.html' %}
            {% endif %}
        </div>
    </div>
</div>

<!-- 新增客户模态框 -->
<div class="modal fade" id="addClientModal" tabindex="-1" aria-labelledby="addClientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addClientModalLabel">
                    <i class="bi bi-plus-circle"></i> 添加客户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="addClientContent">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>
</div>



<!-- 编辑客户模态框 -->
<div class="modal fade" id="editClientModal" tabindex="-1" aria-labelledby="editClientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editClientModalLabel">
                    <i class="bi bi-pencil"></i> 编辑客户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="editClientContent">
                <!-- 动态加载内容 -->
            </div>
        </div>
    </div>
</div>

<!-- 标记管理模态框 -->
<div class="modal fade" id="markManagerModal" tabindex="-1" aria-labelledby="markManagerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="markManagerModalLabel">
                    <i class="bi bi-tags"></i> 标记管理 - <span id="markManagerClientName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    为客户配置模板标记的默认值，在生成内容时会自动填充这些值，减少重复输入。
                </div>

                <div id="markManagerContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载标记数据...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveMarkDefaults">
                    <i class="bi bi-check-lg"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.filter-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

/* 操作按钮组样式 */
.btn-group .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0;
}

.btn-group .btn-sm:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn-sm:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.btn-group .btn-sm i {
    font-size: 0.75rem;
    margin-right: 0.25rem;
}

/* 按钮悬停效果 */
.btn-outline-success:hover {
    background-color: #198754;
    border-color: #198754;
}

.btn-outline-info:hover {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}
</style>

<script>
console.log('客户管理页面已加载');

// 检查是否需要在页面加载后自动编辑客户
document.addEventListener('DOMContentLoaded', function() {
    const editClientId = sessionStorage.getItem('editClientAfterRefresh');
    if (editClientId) {
        console.log('🔄 页面刷新后自动编辑客户:', editClientId);
        sessionStorage.removeItem('editClientAfterRefresh');

        // 延迟执行，确保页面完全加载
        setTimeout(() => {
            editClientWithAjax(editClientId);
        }, 500);
    }
});

// 检查CSRF令牌
const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
console.log('🔐 CSRF令牌检查:', csrfToken ? '✅ 存在' : '❌ 不存在', csrfToken);

// 新增客户模态框事件
document.getElementById('addClientModal').addEventListener('show.bs.modal', function() {
    console.log('显示添加客户模态框');
    console.log('正在加载:', '/clients/create');

    // 简化的加载逻辑
    fetch('/simple/clients/create', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('响应状态:', response.status);
        return response.text();
    })
    .then(html => {
        console.log('✅ 新增客户表单加载完成');
        console.log('📄 HTML内容长度:', html.length);
        document.getElementById('addClientContent').innerHTML = html;

        // 检查表单是否正确加载并初始化
        setTimeout(() => {
            const form = document.getElementById('client-form');
            const submitBtn = document.getElementById('submit-btn');
            console.log('🔍 表单加载检查:', {
                form: form,
                submitBtn: submitBtn,
                formHTML: form ? form.outerHTML.substring(0, 200) + '...' : 'null'
            });

            // 手动初始化表单
            if (form && submitBtn) {
                initializeClientFormManually(form, submitBtn, false);
            }

            // 手动触发标签输入初始化
            console.log('🏷️ 手动触发标签输入初始化...');
            initializeClientTagInputs();
        }, 200);
    })
    .catch(error => {
        console.error('❌ 新增客户表单加载失败:', error);
        document.getElementById('addClientContent').innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
    });
});

// 编辑客户模态框事件
// 编辑客户模态框事件
document.getElementById('editClientModal').addEventListener('show.bs.modal', function() {
    console.log('显示编辑客户模态框');
});

// 编辑客户函数 - 简化版本，强制刷新页面
window.editClient = function(clientId) {
    console.log('🔧 编辑客户:', clientId);

    // 检查是否是刚创建的客户（通过检查高亮状态）
    const clientRow = document.querySelector(`tr td:first-child`);
    let isRecentlyCreated = false;

    // 查找是否有高亮的客户行
    const highlightedRows = document.querySelectorAll('tr[style*="background-color"]');
    if (highlightedRows.length > 0) {
        highlightedRows.forEach(row => {
            const firstCell = row.querySelector('td:first-child');
            if (firstCell && firstCell.textContent.trim() === clientId.toString()) {
                isRecentlyCreated = true;
            }
        });
    }

    if (isRecentlyCreated) {
        console.log('🔄 检测到新创建的客户，强制刷新页面后编辑...');
        // 保存要编辑的客户ID到sessionStorage
        sessionStorage.setItem('editClientAfterRefresh', clientId);
        // 强制刷新页面
        window.location.reload();
        return;
    }

    // 正常的AJAX编辑流程
    console.log('📝 使用AJAX编辑客户...');
    editClientWithAjax(clientId);
};

// AJAX编辑客户函数
function editClientWithAjax(clientId) {
    const modalElement = document.getElementById('editClientModal');
    modalElement.dataset.clientId = clientId;

    console.log('正在加载:', `/simple/clients/${clientId}/edit`);

    fetch(`/simple/clients/${clientId}/edit`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('✅ 编辑客户表单加载完成');
        document.getElementById('editClientContent').innerHTML = html;

        // 显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 简化的初始化
        setTimeout(() => {
            const form = document.getElementById('client-form');
            const submitBtn = document.getElementById('submit-btn');

            if (form && submitBtn) {
                initializeClientFormManually(form, submitBtn, true);
                // 简单的标签初始化
                initializeClientTagInputs();
            }
        }, 200);
    })
    .catch(error => {
        console.error('❌ 编辑客户表单加载失败:', error);
        document.getElementById('editClientContent').innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    });
}

// 切换客户状态函数
window.toggleClientStatus = function(clientId) {
    console.log('切换客户状态:', clientId);

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    console.log('CSRF令牌:', csrfToken);

    // 直接切换，不需要确认对话框
    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/simple/clients/${clientId}/toggle_status`, {
        method: 'POST',
        headers: headers
    })
    .then(response => {
        console.log('状态切换响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('状态切换响应数据:', data);
        if (data.success) {
            // 更新页面上的状态显示，不刷新页面
            updateClientStatusDisplay(clientId, data.status);
            showToast(data.message, 'success');
        } else {
            showToast(data.message || '操作失败', 'danger');
        }
    })
    .catch(error => {
        console.error('切换状态失败:', error);
        showToast('操作失败，请重试', 'danger');
    });
};

// 切换客户审核状态函数
window.toggleClientReview = function(clientId) {
    console.log('切换客户审核状态:', clientId);

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    console.log('CSRF令牌:', csrfToken);

    // 直接切换，不需要确认对话框
    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/simple/clients/${clientId}/toggle_review`, {
        method: 'POST',
        headers: headers
    })
    .then(response => {
        console.log('审核状态切换响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('审核状态切换响应数据:', data);
        if (data.success) {
            // 更新页面上的审核状态显示，不刷新页面
            updateClientReviewDisplay(clientId, data.need_review);
            showToast(data.message, 'success');
        } else {
            showToast(data.message || '操作失败', 'danger');
        }
    })
    .catch(error => {
        console.error('切换审核状态失败:', error);
        showToast('操作失败，请重试', 'danger');
    });
};

// 更新客户状态显示函数
function updateClientStatusDisplay(clientId, newStatus) {
    console.log('更新客户状态显示:', clientId, newStatus);

    // 查找对应的状态标签
    const statusBadge = document.querySelector(`span[onclick="toggleClientStatus(${clientId})"]`);

    if (statusBadge) {
        // 更新文本
        statusBadge.textContent = newStatus ? '启用' : '禁用';

        // 更新样式
        statusBadge.className = `badge ${newStatus ? 'bg-success' : 'bg-secondary'} status-badge`;

        console.log('✅ 状态显示已更新');
    } else {
        console.error('❌ 找不到状态标签');
    }
}

// 更新客户审核状态显示函数
function updateClientReviewDisplay(clientId, needReview) {
    console.log('更新客户审核状态显示:', clientId, needReview);

    // 查找对应的审核状态标签
    const reviewBadge = document.querySelector(`span[onclick="toggleClientReview(${clientId})"]`);

    if (reviewBadge) {
        // 更新文本
        reviewBadge.textContent = needReview ? '需要审核' : '无需审核';

        // 更新样式
        reviewBadge.className = `badge ${needReview ? 'bg-primary' : 'bg-secondary'} review-badge`;

        console.log('✅ 审核状态显示已更新');
    } else {
        console.error('❌ 找不到审核状态标签');
    }
}







// 删除客户函数
window.deleteClient = function(clientId, clientName) {
    console.log('🗑️ 删除客户:', clientId, clientName);

    // 获取CSRF令牌
    const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
    console.log('🔐 删除操作CSRF令牌:', csrfToken ? '✅ 存在' : '❌ 不存在');

    if (!csrfToken) {
        showToast('CSRF令牌不存在，请刷新页面重试', 'danger');
        return;
    }

    // 先获取客户的详细统计信息
    fetch(`/simple/clients/${clientId}/delete-stats`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示详细的删除确认弹窗
            showDeleteConfirmModal(clientId, data.client_name, data.stats, csrfToken);
        } else {
            showToast('获取客户信息失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('获取客户统计信息失败:', error);
        showToast('获取客户信息失败，请重试', 'danger');
    });
};

// 显示删除确认弹窗
function showDeleteConfirmModal(clientId, clientName, stats, csrfToken) {
    const modalHtml = `
        <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="deleteConfirmModalLabel">
                            <i class="bi bi-exclamation-triangle"></i> 永久删除客户
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <strong>⚠️ 警告：此操作不可恢复！</strong><br>
                            删除客户将永久删除所有相关数据，无法恢复。
                        </div>

                        <h6>即将删除客户：<strong>${clientName}</strong></h6>

                        <div class="mt-3">
                            <h6>将被永久删除的数据：</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-file-text text-primary"></i> 文案</span>
                                    <span class="badge bg-primary rounded-pill">${stats.contents} 篇</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-image text-success"></i> 图片</span>
                                    <span class="badge bg-success rounded-pill">${stats.images} 张</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-list-task text-warning"></i> 任务</span>
                                    <span class="badge bg-warning rounded-pill">${stats.tasks} 个</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-collection text-info"></i> 批次</span>
                                    <span class="badge bg-info rounded-pill">${stats.batches} 个</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-tags text-secondary"></i> 标记默认值</span>
                                    <span class="badge bg-secondary rounded-pill">${stats.mark_defaults || 0} 个</span>
                                </li>

                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-link-45deg text-dark"></i> 分享链接</span>
                                    <span class="badge bg-dark rounded-pill">${stats.share_links || 0} 个</span>
                                </li>
                            </ul>
                        </div>

                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmDelete">
                                <label class="form-check-label text-danger" for="confirmDelete">
                                    我确认要永久删除客户 <strong>${clientName}</strong> 及其所有数据
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x"></i> 取消
                        </button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn" disabled>
                            <i class="bi bi-trash"></i> 永久删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('deleteConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新的模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 绑定确认复选框事件
    const confirmCheckbox = document.getElementById('confirmDelete');
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    confirmCheckbox.addEventListener('change', function() {
        confirmBtn.disabled = !this.checked;
    });

    // 绑定删除按钮事件
    confirmBtn.addEventListener('click', function() {
        executeClientDelete(clientId, clientName, csrfToken);
    });

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();

    // 模态框关闭时清理
    document.getElementById('deleteConfirmModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 执行客户删除
function executeClientDelete(clientId, clientName, csrfToken) {
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    const originalText = confirmBtn.innerHTML;

    // 显示加载状态
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="spinner-border spinner-border-sm"></i> 删除中...';

    const headers = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    };

    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    fetch(`/simple/clients/${clientId}/delete`, {
        method: 'POST',
        headers: headers
    })
    .then(response => {
        console.log('📨 删除响应状态:', response.status);
        if (!response.ok) {
            return response.text().then(text => {
                console.error('❌ 删除服务器错误响应:', text);
                throw new Error(`HTTP ${response.status}: ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('📨 删除响应数据:', data);
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();

            // 移除表格行
            removeClientRow(clientId);

            // 显示成功消息，包含统计信息
            let message = data.message;
            if (data.stats) {
                message += `（文案${data.stats.contents}篇，图片${data.stats.images}张，任务${data.stats.tasks}个，批次${data.stats.batches}个）`;
            }
            showToast(message, 'success');
        } else {
            showToast(data.message || '删除失败', 'danger');
            // 恢复按钮状态
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('❌ 删除操作失败:', error);
        showToast('删除失败，请重试', 'danger');
        // 恢复按钮状态
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;
    });
}

// 移除客户行函数
function removeClientRow(clientId) {
    console.log('移除客户行:', clientId);

    // 查找包含客户ID的表格行
    const rows = document.querySelectorAll('tbody tr');
    let targetRow = null;

    for (const row of rows) {
        const firstCell = row.querySelector('td:first-child');
        if (firstCell && firstCell.textContent.trim() === clientId.toString()) {
            targetRow = row;
            break;
        }
    }

    if (targetRow) {
        // 添加淡出动画
        targetRow.style.transition = 'opacity 0.3s';
        targetRow.style.opacity = '0';

        // 延迟移除元素
        setTimeout(() => {
            targetRow.remove();
            console.log('✅ 客户行已移除');
        }, 300);
    } else {
        console.error('❌ 找不到客户行:', clientId);
        // 如果找不到行，就刷新整个列表
        refreshClientList();
    }
}

// 手动初始化客户表单函数
function initializeClientFormManually(form, submitBtn, isEdit) {
    console.log('🔧 手动初始化客户表单:', { isEdit: isEdit });

    // 设置开关事件（移除之前的事件监听器）
    const needReviewSwitch = document.getElementById('need_review_switch');
    const needReviewText = document.getElementById('need_review_text');
    if (needReviewSwitch && needReviewText) {
        // 移除之前的事件监听器
        if (needReviewSwitch._changeHandler) {
            needReviewSwitch.removeEventListener('change', needReviewSwitch._changeHandler);
        }

        const reviewChangeHandler = function() {
            needReviewText.textContent = this.checked ? '需要审核' : '无需审核';
        };

        needReviewSwitch.addEventListener('change', reviewChangeHandler);
        needReviewSwitch._changeHandler = reviewChangeHandler;
        console.log('✅ 审核开关事件已绑定');
    }

    const statusSwitch = document.getElementById('status_switch');
    const statusText = document.getElementById('status_text');
    if (statusSwitch && statusText) {
        // 移除之前的事件监听器
        if (statusSwitch._changeHandler) {
            statusSwitch.removeEventListener('change', statusSwitch._changeHandler);
        }

        const statusChangeHandler = function() {
            statusText.textContent = this.checked ? '启用' : '禁用';
        };

        statusSwitch.addEventListener('change', statusChangeHandler);
        statusSwitch._changeHandler = statusChangeHandler;
        console.log('✅ 状态开关事件已绑定');
    }

    // 移除之前的事件监听器（如果存在）
    const existingHandler = submitBtn._clientFormHandler;
    if (existingHandler) {
        submitBtn.removeEventListener('click', existingHandler);
        console.log('🗑️ 移除了之前的事件处理器');
    }

    // 创建新的事件处理器
    const clickHandler = function(e) {
        e.preventDefault();
        console.log('🔄 提交客户表单');
        console.log('🔍 表单元素:', form);
        console.log('🔍 提交按钮:', submitBtn);

        // 检查表单是否存在
        if (!form) {
            console.error('❌ 表单不存在');
            alert('错误：找不到表单');
            return;
        }

        // 在提交前同步标签数据到隐藏字段
        console.log('🔄 同步标签数据到隐藏字段...');
        syncTagsToHiddenFields();

        // 创建FormData
        const formData = new FormData(form);

        // 获取提交URL
        let clientId = form.dataset.clientId;

        // 如果表单中没有clientId，尝试从模态框中获取
        if (!clientId && isEdit) {
            const modalElement = document.getElementById('editClientModal');
            clientId = modalElement?.dataset?.clientId;
        }

        const submitUrl = isEdit ? `/simple/clients/${clientId}/edit` : '/simple/clients/create';

        // 获取CSRF令牌
        const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

        // 确保FormData包含CSRF令牌
        if (csrfToken) {
            formData.set('csrf_token', csrfToken);
        }

        console.log('📝 表单提交信息:', {
            isEdit: isEdit,
            clientId: clientId,
            submitUrl: submitUrl,
            csrfToken: csrfToken,
            formData: Array.from(formData.entries())
        });

        // 验证编辑模式下的客户ID
        if (isEdit && !clientId) {
            console.error('❌ 编辑模式下缺少客户ID');
            alert('错误：无法获取客户ID，请重新打开编辑窗口');
            return;
        }

        // 设置请求头
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        // 提交表单
        fetch(submitUrl, {
            method: 'POST',
            body: formData,
            headers: headers
        })
        .then(response => {
            console.log('📨 响应状态:', response.status);
            console.log('📨 响应头:', response.headers);
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('❌ 服务器错误响应:', text);
                    throw new Error(`HTTP ${response.status}: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('📨 服务器响应:', data);

            if (data.success) {
                showToast(data.message || '保存成功！', 'success');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
                if (modal) {
                    modal.hide();
                }

                // 简化逻辑：无论编辑还是新增，都刷新整个列表
                setTimeout(() => {
                    if (isEdit) {
                        // 编辑模式：刷新列表并高亮当前客户
                        refreshClientList(clientId);
                    } else {
                        // 新增模式：刷新列表并高亮新客户
                        refreshClientList(data.client_id || null);
                    }
                }, 300);
            } else {
                let errorMessage = data.message || '保存失败';
                if (data.errors) {
                    console.log('📝 表单验证错误:', data.errors);
                    // 显示具体的验证错误
                    const errorDetails = Object.values(data.errors).flat().join(', ');
                    errorMessage += ': ' + errorDetails;
                }
                showToast(errorMessage, 'danger');
            }
        })
        .catch(error => {
            console.error('❌ 提交失败:', error);
            showToast('提交失败：' + error.message, 'danger');
        });
    };

    // 绑定新的事件处理器并保存引用
    submitBtn.addEventListener('click', clickHandler);
    submitBtn._clientFormHandler = clickHandler;
    console.log('✅ 新的事件处理器已绑定');

    console.log('✅ 客户表单手动初始化完成');
}

// 移除了重复的loadModalContent函数，使用内联fetch

// Toast提示函数
function showToast(message, type = 'info') {
    // 创建toast元素
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'danger' ? 'danger' : 'info'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    // 添加到页面
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // 自动移除
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// 刷新客户列表函数
function refreshClientList(highlightClientId = null) {
    console.log('🔄 刷新客户列表', highlightClientId ? `高亮客户ID: ${highlightClientId}` : '');

    fetch('/simple/clients', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.text();
    })
    .then(html => {
        console.log('✅ 客户列表刷新成功');

        // 解析HTML并更新客户表格
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // 找到新的客户表格
        const newTable = tempDiv.querySelector('.table-responsive');
        const currentTable = document.querySelector('.table-responsive');

        if (newTable && currentTable) {
            // 更平滑的更新效果
            currentTable.style.transition = 'opacity 0.2s ease';
            currentTable.style.opacity = '0.8';

            // 替换表格内容
            setTimeout(() => {
                currentTable.innerHTML = newTable.innerHTML;
                currentTable.style.opacity = '1';
                console.log('✅ 客户表格已更新');

                // 高亮显示指定的客户行
                if (highlightClientId) {
                    setTimeout(() => {
                        highlightClientRow(highlightClientId);
                    }, 100);
                }

                // 只在新增客户时显示提示
                if (highlightClientId) {
                    showToast('新客户已添加', 'success');
                }
            }, 150);
        } else {
            console.error('❌ 找不到客户表格');
        }
    })
    .catch(error => {
        console.error('❌ 刷新客户列表失败:', error);
        showToast('刷新列表失败，请手动刷新页面', 'warning');
    });
}

// 高亮显示客户行函数
function highlightClientRow(clientId) {
    console.log('🎯 高亮显示客户行:', clientId);

    // 查找包含客户ID的表格行
    const rows = document.querySelectorAll('tbody tr');
    let targetRow = null;

    for (const row of rows) {
        const firstCell = row.querySelector('td:first-child');
        if (firstCell && firstCell.textContent.trim() === clientId.toString()) {
            targetRow = row;
            break;
        }
    }

    if (targetRow) {
        // 添加高亮效果
        targetRow.style.backgroundColor = '#d4edda';
        targetRow.style.transition = 'background-color 0.5s ease';

        // 滚动到该行
        targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 3秒后移除高亮效果
        setTimeout(() => {
            targetRow.style.backgroundColor = '';
        }, 3000);

        console.log('✅ 客户行已高亮');
    } else {
        console.log('❌ 找不到要高亮的客户行:', clientId);
    }
}

// 更新单个客户行数据函数
function updateClientRowData(clientId) {
    console.log('🔄 更新客户行数据:', clientId);

    // 获取最新的客户数据
    fetch(`/simple/clients/${clientId}/data`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(response => {
        console.log('✅ 获取客户数据成功:', response);

        // 检查响应格式
        if (!response.success || !response.client) {
            throw new Error('无效的响应格式');
        }

        const clientData = response.client;

        // 查找对应的表格行
        const rows = document.querySelectorAll('tbody tr');
        let targetRow = null;

        for (const row of rows) {
            const firstCell = row.querySelector('td:first-child');
            if (firstCell && firstCell.textContent.trim() === clientId.toString()) {
                targetRow = row;
                break;
            }
        }

        if (targetRow) {
            // 更新行数据
            const cells = targetRow.querySelectorAll('td');
            if (cells.length >= 6) {
                // 更新客户名称 (第2列)
                cells[1].textContent = clientData.name || '-';

                // 更新联系人 (第3列)
                cells[2].textContent = clientData.contact || '-';

                // 更新审核状态 (第4列，索引3)
                const reviewBadge = cells[3].querySelector('span');
                if (reviewBadge) {
                    reviewBadge.textContent = clientData.need_review ? '需要审核' : '无需审核';
                    reviewBadge.className = `badge ${clientData.need_review ? 'bg-primary' : 'bg-secondary'} review-badge`;
                }

                // 更新文章数 (第5列，索引4)
                const contentBadge = cells[4].querySelector('span');
                if (contentBadge) {
                    contentBadge.textContent = clientData.total_content_count || 0;
                }

                // 更新发布数 (第6列，索引5)
                const publishedBadge = cells[5].querySelector('span');
                if (publishedBadge) {
                    publishedBadge.textContent = clientData.published_count || 0;
                }

                // 更新状态 (第7列，索引6)
                const statusBadge = cells[6].querySelector('span');
                if (statusBadge) {
                    statusBadge.textContent = clientData.status ? '启用' : '禁用';
                    statusBadge.className = `badge ${clientData.status ? 'bg-success' : 'bg-secondary'} status-badge`;
                }

                // 添加轻微的高亮效果表示更新
                targetRow.style.backgroundColor = '#e3f2fd';
                targetRow.style.transition = 'background-color 0.3s ease';

                setTimeout(() => {
                    targetRow.style.backgroundColor = '';
                }, 1500);

                console.log('✅ 客户行数据已更新');
                showToast('客户信息已更新', 'success');
            }
        } else {
            console.error('❌ 找不到要更新的客户行:', clientId);
            // 如果找不到行，降级到刷新整个列表
            refreshClientList(clientId);
        }
    })
    .catch(error => {
        console.error('❌ 获取客户数据失败:', error);
        // 出错时降级到刷新整个列表
        refreshClientList(clientId);
    });
}

// 简化版客户管理分页功能
// 排序功能
document.querySelectorAll('.sort-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const sortBy = this.getAttribute('data-sort');

        // 更新按钮状态
        document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        // 构建新的URL参数
        const url = new URL(window.location);
        if (sortBy === 'default') {
            url.searchParams.delete('sort_by');
        } else {
            url.searchParams.set('sort_by', sortBy);
        }

        // 跳转到新URL
        window.location.href = url.toString();
    });
});

console.log('客户管理页面已加载');

// 筛选客户 - 全局函数，确保按钮可以调用
window.filterClients = function() {
    const statusElement = document.getElementById('status');
    const searchElement = document.getElementById('search');
    const status = statusElement ? statusElement.value : '';
    const search = searchElement ? searchElement.value.trim() : '';

    // 获取当前的每页显示数量，保持用户设置
    // 优先从分页组件获取，如果没有则从URL参数获取，最后默认20
    const paginationContainer = document.querySelector('#client-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    let currentPerPage = perPageSelect?.value;

    // 如果分页组件中没有找到，尝试从URL参数获取
    if (!currentPerPage) {
        const urlParams = new URLSearchParams(window.location.search);
        currentPerPage = urlParams.get('per_page') || '20';
    }

    console.log('筛选客户详细信息:', {
        status: status,
        statusEmpty: status === '',
        search: search,
        currentPerPage: currentPerPage,
        statusElement: !!statusElement,
        searchElement: !!searchElement
    });

    // 构建查询参数，包含每页显示数量
    const params = new URLSearchParams();
    params.set('page', '1'); // 筛选时重置到第一页
    params.set('per_page', currentPerPage); // 保持当前每页显示数量

    // 只有当状态不为空时才添加状态参数
    if (status !== '' && status !== null && status !== undefined) {
        params.set('status', status);
        console.log('添加状态筛选:', status);
    }

    // 只有当搜索不为空时才添加搜索参数
    if (search !== '' && search !== null && search !== undefined) {
        params.set('search', search);
        console.log('添加搜索筛选:', search);
    }

    console.log('发送筛选请求:', `/simple/clients?${params.toString()}`);

    // 重新加载客户列表
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
            console.log('筛选完成，页面已更新');
        }
    })
    .catch(error => {
        console.error('筛选失败:', error);
        alert('筛选失败，请重试');
    });
};

// 重置筛选 - 全局函数
window.resetFilter = function() {
    document.getElementById('status').value = '1';  // 重置到默认状态：启用
    document.getElementById('search').value = '';

    // 获取当前的每页显示数量，保持用户设置
    const paginationContainer = document.querySelector('#client-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    const currentPerPage = perPageSelect?.value || 20;

    console.log('重置筛选，保持每页显示数量:', currentPerPage);

    // 构建查询参数，包含默认状态和每页显示数量
    const params = new URLSearchParams();
    params.set('page', '1');
    params.set('per_page', currentPerPage);
    params.set('status', '1');  // 默认显示启用状态

    // 重新加载客户列表
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
            console.log('重置完成，页面已更新');
        }
    })
    .catch(error => {
        console.error('重置失败:', error);
        alert('重置失败，请重试');
    });
};

// 定义简化版本的分页函数
function simpleClientChangePageSize(newSize) {
    // 获取当前的筛选参数
    const statusElement = document.getElementById('status');
    const searchElement = document.getElementById('search');
    const status = statusElement ? statusElement.value : '';
    const search = searchElement ? searchElement.value.trim() : '';

    console.log('改变每页显示数量:', { newSize, status, search });

    // 构建查询参数
    const params = new URLSearchParams();
    params.set('per_page', newSize);
    params.set('page', '1'); // 重置到第一页

    // 只有当状态不为空时才添加状态参数
    if (status !== '' && status !== null && status !== undefined) {
        params.set('status', status);
    }

    // 只有当搜索不为空时才添加搜索参数
    if (search !== '' && search !== null && search !== undefined) {
        params.set('search', search);
    }

    console.log('改变每页数量请求:', `/simple/clients?${params.toString()}`);

    // 使用AJAX重新加载
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
        }
    })
    .catch(error => {
        console.error('更新失败:', error);
        alert('更新失败，请重试');
    });
}

function simpleClientChangePage(pageNum) {
    // 获取当前的筛选参数和每页显示数量
    const statusElement = document.getElementById('status');
    const searchElement = document.getElementById('search');
    const status = statusElement ? statusElement.value : '';
    const search = searchElement ? searchElement.value.trim() : '';

    // 更精确地获取分页组件中的每页显示数量选择器
    const paginationContainer = document.querySelector('#client-list-container');
    const perPageSelect = paginationContainer?.querySelector('.form-select');
    const perPage = perPageSelect?.value || 20;

    console.log('翻页操作:', { pageNum, perPage, status, search });

    // 构建查询参数
    const params = new URLSearchParams();
    params.set('page', pageNum);
    params.set('per_page', perPage);

    // 只有当状态不为空时才添加状态参数
    if (status !== '' && status !== null && status !== undefined) {
        params.set('status', status);
    }

    // 只有当搜索不为空时才添加搜索参数
    if (search !== '' && search !== null && search !== undefined) {
        params.set('search', search);
    }

    console.log('翻页请求:', `/simple/clients?${params.toString()}`);

    // 使用AJAX重新加载
    fetch(`/simple/clients?${params.toString()}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.text())
    .then(html => {
        // 只更新客户列表容器，保持页面布局
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newListContainer = doc.querySelector('#client-list-container');
        if (newListContainer) {
            document.querySelector('#client-list-container').innerHTML = newListContainer.innerHTML;
            // 重新设置分页函数，确保新的分页组件使用我们的函数
            window.changePageSize = simpleClientChangePageSize;
            window.changePage = simpleClientChangePage;
        }
    })
    .catch(error => {
        console.error('翻页失败:', error);
        alert('翻页失败，请重试');
    });
}

// 设置全局函数供分页组件使用
window.changePageForSimple = simpleClientChangePage;

// 保存原来的分页函数（如果存在）
window.originalChangePageSize = window.changePageSize;
window.originalChangePage = window.changePage;

// 设置当前页面的分页函数
window.changePageSize = simpleClientChangePageSize;
window.changePage = simpleClientChangePage;







// 调试函数：检查所有隐藏字段的值
function debugHiddenFields() {
    console.log('🔍 调试隐藏字段值:');
    const fields = [
        'default_required_topics',
        'default_random_topics',
        'default_at_users',
        'default_location'
    ];

    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            console.log(`📝 ${fieldId}:`, field.value);
        } else {
            console.log(`❌ ${fieldId}: 字段不存在`);
        }
    });
}

// 全局调试函数，可以在控制台手动调用
window.debugClientForm = function() {
    console.log('🐛 手动调试客户表单...');
    debugHiddenFields();

    // 检查容器
    const containers = [
        'default-required-topics-container',
        'default-random-topics-container',
        'default-at-users-container',
        'default-location-container'
    ];

    containers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            const tags = container.querySelectorAll('.tag');
            console.log(`📦 ${containerId}: ${tags.length} 个标签`);
        } else {
            console.log(`❌ ${containerId}: 容器不存在`);
        }
    });
};

// 同步标签数据到隐藏字段的函数
function syncTagsToHiddenFields() {
    console.log('🔄 开始同步标签数据...');

    const tagMappings = [
        { containerId: 'default-required-topics-container', hiddenFieldId: 'default_required_topics' },
        { containerId: 'default-random-topics-container', hiddenFieldId: 'default_random_topics' },
        { containerId: 'default-at-users-container', hiddenFieldId: 'default_at_users' },
        { containerId: 'default-location-container', hiddenFieldId: 'default_location' }
    ];

    tagMappings.forEach(mapping => {
        const container = document.getElementById(mapping.containerId);
        const hiddenField = document.getElementById(mapping.hiddenFieldId);

        if (container && hiddenField) {
            const tags = container.querySelectorAll('.tag');
            const values = [];

            tags.forEach(tag => {
                const text = tag.textContent.replace('×', '').trim();
                if (text) {
                    values.push(text);
                }
            });

            const newValue = values.join('\n');
            hiddenField.value = newValue;
            console.log(`🔄 同步字段 ${mapping.hiddenFieldId}:`, newValue);
        } else {
            console.warn(`⚠️ 找不到元素: ${mapping.containerId} 或 ${mapping.hiddenFieldId}`);
        }
    });

    console.log('✅ 标签数据同步完成');
}

// 简化的编辑表单标签初始化函数（移除复杂逻辑）
function initializeEditFormTagInputsSimple() {
    console.log('🏷️ 开始核心标签输入初始化...');

    // 复制模态框中的标签初始化逻辑
    function addTag(value, containerId, hiddenFieldId) {
        console.log('📝 addTag 调用:', { value, containerId, hiddenFieldId });

        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);

        if (!container) {
            console.error('❌ 找不到容器:', containerId);
            return false;
        }

        if (!hiddenField) {
            console.error('❌ 找不到隐藏字段:', hiddenFieldId);
            return false;
        }

        // 检查是否已存在相同标签
        const existingTags = container.querySelectorAll('.tag');
        for (let i = 0; i < existingTags.length; i++) {
            const existingText = existingTags[i].textContent.trim().replace('×', '').trim();
            if (existingText === value) {
                console.log('⚠️ 标签已存在，不重复添加:', value);
                return false;
            }
        }

        // 创建新标签
        const tag = document.createElement('span');
        tag.className = 'tag';
        tag.innerHTML = `${value} <span class="remove" onclick="removeClientTag(this, '${hiddenFieldId}')">×</span>`;

        // 添加到容器
        container.appendChild(tag);
        updateContainerEmptyState(container);

        // 更新隐藏字段
        updateHiddenField(containerId, hiddenFieldId);

        console.log('✅ 标签添加成功:', value);
        return true;
    }

    // 更新容器空状态
    function updateContainerEmptyState(container) {
        const tags = container.querySelectorAll('.tag');
        if (tags.length === 0) {
            container.classList.add('empty');
        } else {
            container.classList.remove('empty');
        }
    }

    // 更新隐藏字段
    function updateHiddenField(containerId, hiddenFieldId) {
        console.log('🔄 updateHiddenField:', { containerId, hiddenFieldId });

        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);

        if (!container || !hiddenField) {
            console.error('❌ 找不到容器或隐藏字段:', { containerId, hiddenFieldId });
            return;
        }

        const tags = container.querySelectorAll('.tag');
        const values = [];

        tags.forEach(tag => {
            const text = tag.textContent.replace('×', '').trim();
            if (text) {
                values.push(text);
            }
        });

        // 所有字段都支持多个值，用换行符分隔
        hiddenField.value = values.join('\n');

        console.log('💾 隐藏字段已更新:', hiddenFieldId, '=', hiddenField.value);
    }

    // 删除标签函数
    window.removeClientTag = function(removeBtn, hiddenFieldId) {
        console.log('🗑️ removeClientTag 调用:', hiddenFieldId);

        const tag = removeBtn.parentElement;
        const container = tag.parentElement;
        const containerId = container.id;

        console.log('🗑️ 删除标签详情:', {
            tagText: tag.textContent.replace('×', '').trim(),
            containerId: containerId,
            hiddenFieldId: hiddenFieldId
        });

        tag.remove();
        updateContainerEmptyState(container);
        updateHiddenField(containerId, hiddenFieldId);

        console.log('🗑️ 标签删除完成');
    };

    // 初始化各个输入框的事件监听器
    const inputMappings = [
        { inputId: 'default-required-topics-input', containerId: 'default-required-topics-container', hiddenFieldId: 'default_required_topics' },
        { inputId: 'default-random-topics-input', containerId: 'default-random-topics-container', hiddenFieldId: 'default_random_topics' },
        { inputId: 'default-at-users-input', containerId: 'default-at-users-container', hiddenFieldId: 'default_at_users' },
        { inputId: 'default-location-input', containerId: 'default-location-container', hiddenFieldId: 'default_location' }
    ];

    inputMappings.forEach(mapping => {
        const input = document.getElementById(mapping.inputId);
        if (input) {
            console.log('🔧 初始化输入框:', mapping.inputId);

            // 回车事件
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const value = this.value.trim();
                    if (value) {
                        // 特殊处理@用户字段
                        if (mapping.hiddenFieldId === 'default_at_users' && !value.startsWith('@')) {
                            addTag('@' + value, mapping.containerId, mapping.hiddenFieldId);
                        } else {
                            addTag(value, mapping.containerId, mapping.hiddenFieldId);
                        }
                        this.value = '';
                    }
                }
            });

            // 失焦事件
            input.addEventListener('blur', function() {
                const value = this.value.trim();
                if (value) {
                    // 特殊处理@用户字段
                    if (mapping.hiddenFieldId === 'default_at_users' && !value.startsWith('@')) {
                        addTag('@' + value, mapping.containerId, mapping.hiddenFieldId);
                    } else {
                        addTag(value, mapping.containerId, mapping.hiddenFieldId);
                    }
                    this.value = '';
                }
            });

            // 粘贴事件
            input.addEventListener('paste', function(e) {
                setTimeout(() => {
                    const pastedText = this.value;
                    if (pastedText.includes('\n')) {
                        const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);
                        lines.forEach(line => {
                            // 特殊处理@用户字段
                            if (mapping.hiddenFieldId === 'default_at_users' && !line.startsWith('@')) {
                                addTag('@' + line, mapping.containerId, mapping.hiddenFieldId);
                            } else {
                                addTag(line, mapping.containerId, mapping.hiddenFieldId);
                            }
                        });
                        this.value = '';
                    }
                }, 10);
            });
        }
    });

    // 从隐藏字段加载现有数据
    function loadExistingData() {
        console.log('📂 开始加载现有数据...');

        const mappings = [
            { hiddenId: 'default_required_topics', containerId: 'default-required-topics-container' },
            { hiddenId: 'default_random_topics', containerId: 'default-random-topics-container' },
            { hiddenId: 'default_at_users', containerId: 'default-at-users-container' },
            { hiddenId: 'default_location', containerId: 'default-location-container' }
        ];

        mappings.forEach(mapping => {
            const hiddenField = document.getElementById(mapping.hiddenId);
            const container = document.getElementById(mapping.containerId);

            if (hiddenField && container) {
                // 先清空容器中的所有标签，避免重复
                console.log('🧹 清空容器:', mapping.containerId);
                container.innerHTML = '';
                container.classList.add('empty');

                const value = hiddenField.value;
                console.log('📂 加载字段数据:', mapping.hiddenId, '=', value);

                if (value) {
                    const values = value.split('\n').map(v => v.trim()).filter(v => v);
                    console.log('📂 分割后的值:', values);

                    values.forEach(val => {
                        addTag(val, mapping.containerId, mapping.hiddenId);
                    });
                    updateContainerEmptyState(container);
                }
            } else {
                console.warn('⚠️ 找不到字段或容器:', mapping);
            }
        });

        console.log('📂 现有数据加载完成');
    }

    // 延迟加载现有数据，确保DOM完全准备好
    setTimeout(loadExistingData, 100);

    console.log('✅ 简化标签输入初始化完成');
}

// 全局函数：初始化客户标签输入
window.initializeClientTagInputs = function() {
    console.log('🏷️ 开始初始化客户标签输入...');

    // 先调试隐藏字段的值
    debugHiddenFields();

    // 检查关键元素是否存在
    const requiredElements = [
        'default-required-topics-input',
        'default-required-topics-container',
        'default_required_topics'
    ];

    const allExists = requiredElements.every(id => {
        const element = document.getElementById(id);
        const exists = !!element;
        console.log('🔍 检查元素:', id, exists ? '✅' : '❌', element ? `(值: "${element.value || 'N/A'}")` : '');
        return exists;
    });

    if (!allExists) {
        console.warn('⚠️ 标签输入元素未找到，可能表单还未加载完成');
        // 尝试延迟重试
        setTimeout(() => {
            console.log('🔄 延迟重试初始化客户标签输入...');
            window.initializeClientTagInputs();
        }, 1000);
        return;
    }

    // 直接初始化客户标签输入
    initClientTagInputsDirectly();
};

// 直接初始化客户标签输入的函数
function initClientTagInputsDirectly() {
    console.log('🔧 直接初始化客户标签输入...');

    // 参考内容生成页面的addTag函数
    function addTag(value, containerId, hiddenFieldId) {
        console.log('📝 addTag 调用:', { value, containerId, hiddenFieldId });

        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);

        if (!container || !hiddenField) {
            console.error('❌ 找不到容器或隐藏字段:', { containerId, hiddenFieldId });
            return false;
        }

        // 检查是否已存在相同标签
        const existingTags = container.querySelectorAll('.tag');
        for (let i = 0; i < existingTags.length; i++) {
            const existingText = existingTags[i].textContent.trim().replace('×', '').trim();
            if (existingText === value) {
                console.log('⚠️ 标签已存在，不重复添加:', value);
                return false;
            }
        }

        // 创建新标签
        const tag = document.createElement('span');
        tag.className = 'tag';
        tag.innerHTML = `${value} <span class="remove" onclick="removeClientTag(this, '${hiddenFieldId}')">×</span>`;

        // 添加到容器
        container.appendChild(tag);
        updateContainerEmptyState(container);

        // 更新隐藏字段
        updateHiddenField(containerId, hiddenFieldId);

        console.log('✅ 标签添加成功:', value);
        return true;
    }

    // 更新容器空状态
    function updateContainerEmptyState(container) {
        const tags = container.querySelectorAll('.tag');
        if (tags.length === 0) {
            container.classList.add('empty');
        } else {
            container.classList.remove('empty');
        }
    }

    // 更新隐藏字段
    function updateHiddenField(containerId, hiddenFieldId) {
        const container = document.getElementById(containerId);
        const hiddenField = document.getElementById(hiddenFieldId);

        if (!container || !hiddenField) return;

        const tags = container.querySelectorAll('.tag');
        const values = [];

        tags.forEach(tag => {
            const text = tag.textContent.replace('×', '').trim();
            if (text) {
                values.push(text);
            }
        });

        // 所有字段都支持多个值，用换行符分隔
        hiddenField.value = values.join('\n');

        console.log('💾 隐藏字段已更新:', hiddenFieldId, '=', hiddenField.value);
    }

    // 删除标签函数
    window.removeClientTag = function(removeBtn, hiddenFieldId) {
        const tag = removeBtn.parentElement;
        const container = tag.parentElement;
        const containerId = container.id;
        const tagText = tag.textContent.replace('×', '').trim();

        console.log('🗑️ 删除标签详情:', {
            tagText: tagText,
            containerId: containerId,
            hiddenFieldId: hiddenFieldId
        });

        tag.remove();
        updateContainerEmptyState(container);
        updateHiddenField(containerId, hiddenFieldId);

        console.log('🗑️ 标签删除完成');
    };

    // 添加函数
    function addDefaultRequiredTopic() {
        const input = document.getElementById('default-required-topics-input');
        const value = input.value.trim();
        if (value) {
            addTag(value, 'default-required-topics-container', 'default_required_topics');
            input.value = '';
        }
    }

    function addDefaultRandomTopic() {
        const input = document.getElementById('default-random-topics-input');
        const value = input.value.trim();
        if (value) {
            addTag(value, 'default-random-topics-container', 'default_random_topics');
            input.value = '';
        }
    }

    function addDefaultAtUser() {
        const input = document.getElementById('default-at-users-input');
        let value = input.value.trim();
        if (value) {
            if (!value.startsWith('@')) {
                value = '@' + value;
            }
            addTag(value, 'default-at-users-container', 'default_at_users');
            input.value = '';
        }
    }

    function addDefaultLocation() {
        const input = document.getElementById('default-location-input');
        const value = input.value.trim();
        if (value) {
            addTag(value, 'default-location-container', 'default_location');
            input.value = '';
        }
    }

    // 初始化输入事件
    const inputs = [
        { id: 'default-required-topics-input', handler: addDefaultRequiredTopic },
        { id: 'default-random-topics-input', handler: addDefaultRandomTopic },
        { id: 'default-at-users-input', handler: addDefaultAtUser },
        { id: 'default-location-input', handler: addDefaultLocation }
    ];

    inputs.forEach(({ id, handler }) => {
        const input = document.getElementById(id);
        if (input) {
            console.log('🔧 初始化输入框:', id);

            // 移除现有事件监听器（如果有）
            if (input._keydownHandler) {
                input.removeEventListener('keydown', input._keydownHandler);
            }
            if (input._blurHandler) {
                input.removeEventListener('blur', input._blurHandler);
            }
            if (input._pasteHandler) {
                input.removeEventListener('paste', input._pasteHandler);
            }

            // 添加新的事件监听器
            const keydownHandler = function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('⌨️ 回车事件:', id);
                    handler();
                }
            };

            const blurHandler = function() {
                if (this.value.trim()) {
                    console.log('👁️ 失焦事件:', id);
                    handler();
                }
            };

            // 粘贴事件处理
            const pasteHandler = function(e) {
                e.preventDefault();
                const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                const lines = pastedText.split('\n').map(line => line.trim()).filter(line => line);

                console.log('📋 粘贴事件:', id, '行数:', lines.length);

                // 所有字段都支持多行，每行作为一个标签
                lines.forEach(line => {
                    if (id === 'default-required-topics-input') {
                        addTag(line, 'default-required-topics-container', 'default_required_topics');
                    } else if (id === 'default-random-topics-input') {
                        addTag(line, 'default-random-topics-container', 'default_random_topics');
                    } else if (id === 'default-at-users-input') {
                        let value = line;
                        if (!value.startsWith('@')) {
                            value = '@' + value;
                        }
                        addTag(value, 'default-at-users-container', 'default_at_users');
                    } else if (id === 'default-location-input') {
                        addTag(line, 'default-location-container', 'default_location');
                    }
                });
                this.value = '';
            };

            input.addEventListener('keydown', keydownHandler);
            input.addEventListener('blur', blurHandler);
            input.addEventListener('paste', pasteHandler);

            // 保存引用以便后续移除
            input._keydownHandler = keydownHandler;
            input._blurHandler = blurHandler;
            input._pasteHandler = pasteHandler;

            // 初始化容器空状态
            const containerId = input.getAttribute('data-container');
            if (containerId) {
                const container = document.getElementById(containerId);
                if (container) {
                    updateContainerEmptyState(container);
                }
            }
        }
    });

    // 加载现有数据
    console.log('📂 开始加载现有数据...');
    const mappings = [
        { hiddenId: 'default_required_topics', containerId: 'default-required-topics-container' },
        { hiddenId: 'default_random_topics', containerId: 'default-random-topics-container' },
        { hiddenId: 'default_at_users', containerId: 'default-at-users-container' },
        { hiddenId: 'default_location', containerId: 'default-location-container' }
    ];

    mappings.forEach(mapping => {
        const hiddenField = document.getElementById(mapping.hiddenId);
        const container = document.getElementById(mapping.containerId);

        console.log('📂 检查字段:', mapping.hiddenId, {
            hiddenField: !!hiddenField,
            container: !!container,
            value: hiddenField ? hiddenField.value : 'N/A'
        });

        if (hiddenField && container) {
            const value = hiddenField.value;
            console.log('📂 加载字段数据:', mapping.hiddenId, '=', value);

            if (value) {
                const values = value.split('\n').map(v => v.trim()).filter(v => v);
                console.log('📂 分割后的值:', values);

                values.forEach(val => {
                    console.log('📂 添加标签:', val, '到容器:', mapping.containerId);
                    addTag(val, mapping.containerId, mapping.hiddenId);
                });
                updateContainerEmptyState(container);
            }
        } else {
            console.warn('⚠️ 找不到字段或容器:', mapping);
        }
    });

    console.log('📂 现有数据加载完成');
    console.log('✅ 客户标签输入初始化完成');
}

// 标记管理相关函数
window.openMarkManager = function(clientId, clientName) {
    console.log('🏷️ 打开标记管理:', clientId, clientName);

    // 设置模态框标题
    document.getElementById('markManagerClientName').textContent = clientName;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('markManagerModal'));
    modal.show();

    // 加载标记数据
    loadMarkDefaults(clientId);
};

function loadMarkDefaults(clientId) {
    const contentDiv = document.getElementById('markManagerContent');

    // 显示加载状态
    contentDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载标记数据...</p>
        </div>
    `;

    fetch(`/simple/api/clients/${clientId}/mark-defaults`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderMarkManager(data.marks, clientId);
            } else {
                contentDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        加载失败：${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载标记数据失败:', error);
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    网络错误，请重试
                </div>
            `;
        });
}

function renderMarkManager(marks, clientId) {
    const contentDiv = document.getElementById('markManagerContent');

    if (Object.keys(marks).length === 0) {
        contentDiv.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-info-circle"></i>
                暂无可配置的标记。请先在<a href="/simple/templates" target="_blank">模板管理</a>中的标记管理功能添加标记。
            </div>
        `;
        return;
    }

    let html = '<div class="row">';

    Object.entries(marks).forEach(([markName, values]) => {
        html += `
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-tag"></i> ${markName}
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- 输入框 -->
                        <div class="input-group mb-3">
                            <input type="text" class="form-control"
                                   id="mark-${markName}-input"
                                   placeholder="输入${markName}默认值，回车添加"
                                   onkeypress="handleMarkInputKeypress(event, '${markName}')"
                                   onpaste="handleMarkInputPaste(event, '${markName}')"
                                   onblur="handleMarkInputBlur(event, '${markName}')">
                            <button class="btn btn-primary" type="button"
                                    onclick="addMarkTagsFromInput('${markName}')">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>

                        <!-- 已添加的标签 -->
                        <div class="tag-display-container border rounded p-2 bg-light"
                             id="mark-${markName}-container"
                             style="min-height: 50px;">
                            ${values.map(value => `
                                <span class="badge bg-primary me-1 mb-1">
                                    ${value}
                                    <button type="button" class="btn-close btn-close-white ms-1"
                                            onclick="removeMarkTag(this, '${markName}')"
                                            aria-label="删除"></button>
                                </span>
                            `).join('')}
                            ${values.length === 0 ? '<span class="text-muted small">暂无默认值</span>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    contentDiv.innerHTML = html;

    // 保存客户ID到模态框
    document.getElementById('markManagerModal').dataset.clientId = clientId;
}

function handleMarkInputKeypress(event, markName) {
    if (event.key === 'Enter') {
        event.preventDefault();
        addMarkTagsFromInput(markName);
    }
}

function handleMarkInputBlur(event, markName) {
    // 失去焦点时自动添加输入框中的内容
    addMarkTagsFromInput(markName);
}

function handleMarkInputPaste(event, markName) {
    // 阻止默认粘贴行为
    event.preventDefault();

    // 获取粘贴的内容
    const pasteData = (event.clipboardData || window.clipboardData).getData('text');

    if (pasteData) {
        // 按行分割内容
        const lines = pasteData.split(/\r?\n/).map(line => line.trim()).filter(line => line);

        if (lines.length > 0) {
            // 如果是多行内容，直接处理
            if (lines.length > 1) {
                addMultipleMarkTags(markName, lines);
            } else {
                // 单行内容，填入输入框
                const input = document.getElementById(`mark-${markName}-input`);
                input.value = lines[0];
            }
        }
    }
}

function addMarkTagsFromInput(markName) {
    const input = document.getElementById(`mark-${markName}-input`);
    const value = input.value.trim();

    if (!value) return;

    // 单行输入，直接添加
    addSingleMarkTag(markName, value);

    // 清空输入框
    input.value = '';
}

function addSingleMarkTag(markName, value) {
    if (!value) return;

    const container = document.getElementById(`mark-${markName}-container`);

    // 移除"暂无默认值"提示
    const emptyHint = container.querySelector('.text-muted');
    if (emptyHint) {
        emptyHint.remove();
    }

    // 获取现有标签
    const existingTags = Array.from(container.querySelectorAll('.badge')).map(tag =>
        tag.textContent.replace('×', '').trim()
    );

    if (existingTags.includes(value)) {
        showToast('该值已存在', 'warning');
        return;
    }

    // 添加标签
    const tagHtml = `
        <span class="badge bg-primary me-1 mb-1">
            ${value}
            <button type="button" class="btn-close btn-close-white ms-1"
                    onclick="removeMarkTag(this, '${markName}')"
                    aria-label="删除"></button>
        </span>
    `;

    container.insertAdjacentHTML('beforeend', tagHtml);
    showToast('添加成功', 'success');
}

function addMultipleMarkTags(markName, lines) {
    if (!lines || lines.length === 0) return;

    const container = document.getElementById(`mark-${markName}-container`);

    // 移除"暂无默认值"提示
    const emptyHint = container.querySelector('.text-muted');
    if (emptyHint) {
        emptyHint.remove();
    }

    // 获取现有标签
    const existingTags = Array.from(container.querySelectorAll('.badge')).map(tag =>
        tag.textContent.replace('×', '').trim()
    );

    let addedCount = 0;
    let duplicateCount = 0;

    lines.forEach(line => {
        if (existingTags.includes(line)) {
            duplicateCount++;
            return;
        }

        // 添加标签
        const tagHtml = `
            <span class="badge bg-primary me-1 mb-1">
                ${line}
                <button type="button" class="btn-close btn-close-white ms-1"
                        onclick="removeMarkTag(this, '${markName}')"
                        aria-label="删除"></button>
            </span>
        `;

        container.insertAdjacentHTML('beforeend', tagHtml);
        existingTags.push(line);
        addedCount++;
    });

    // 显示结果提示
    if (addedCount > 0) {
        showToast(`成功添加 ${addedCount} 个默认值${duplicateCount > 0 ? `，跳过 ${duplicateCount} 个重复值` : ''}`, 'success');
    } else if (duplicateCount > 0) {
        showToast(`所有值都已存在，跳过 ${duplicateCount} 个重复值`, 'warning');
    }
}



function removeMarkTag(button, markName) {
    const container = button.parentElement.parentElement;
    button.parentElement.remove();

    // 如果没有标签了，显示提示
    const remainingTags = container.querySelectorAll('.badge');
    if (remainingTags.length === 0) {
        container.innerHTML = '<span class="text-muted small">暂无默认值</span>';
    }
}

// 保存标记默认值
document.addEventListener('DOMContentLoaded', function() {
    const saveButton = document.getElementById('saveMarkDefaults');
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            const modal = document.getElementById('markManagerModal');
            const clientId = modal.dataset.clientId;

            if (!clientId) {
                showToast('客户ID丢失，请重新打开', 'danger');
                return;
            }

            // 先处理所有输入框中的内容
            const inputs = document.querySelectorAll('[id^="mark-"][id$="-input"]');
            inputs.forEach(input => {
                const markName = input.id.replace('mark-', '').replace('-input', '');
                if (input.value.trim()) {
                    addMarkTagsFromInput(markName);
                }
            });

            // 等待DOM更新后再收集数据
            setTimeout(() => {
                // 收集所有标记数据
                const marks = {};
                const containers = document.querySelectorAll('[id^="mark-"][id$="-container"]');

                containers.forEach(container => {
                    const markName = container.id.replace('mark-', '').replace('-container', '');
                    const badges = container.querySelectorAll('.badge');

                    if (badges.length > 0) {
                        const tags = Array.from(badges).map(tag => {
                            // 获取标签文本，移除删除按钮
                            const closeBtn = tag.querySelector('.btn-close');
                            if (closeBtn) {
                                closeBtn.remove(); // 临时移除按钮
                            }
                            const text = tag.textContent.trim();
                            if (closeBtn) {
                                tag.appendChild(closeBtn); // 恢复按钮
                            }
                            return text;
                        }).filter(text => text.length > 0);

                        // 只保存有内容的标记
                        if (tags.length > 0) {
                            marks[markName] = tags;
                        }
                    }

                    console.log(`收集标记 ${markName}:`, marks[markName] || []);
                });

                console.log('收集到的所有标记数据:', marks);

                // 执行保存
                performSave(marks, clientId, this);
            }, 100);
        });
    }
});

// 查看客户详情
window.viewClientDetail = function(clientId, clientName) {
    console.log('查看客户详情:', clientId, clientName);
    // 跳转到客户详情页面
    window.location.href = `/simple/clients/${clientId}/detail`;
};

// 保存标记默认值的函数
function performSave(marks, clientId, saveButton) {
    // 保存数据
    saveButton.disabled = true;
    saveButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>保存中...';

    fetch(`/simple/api/clients/${clientId}/mark-defaults`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ marks: marks })
    })
    .then(response => {
        console.log('响应状态:', response.status);
        if (!response.ok) {
            return response.text().then(text => {
                console.error('服务器错误响应:', text);
                throw new Error(`HTTP ${response.status}: ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('保存响应:', data);
        if (data.success) {
            showToast(data.message, 'success');
            const modal = document.getElementById('markManagerModal');
            bootstrap.Modal.getInstance(modal).hide();
        } else {
            showToast(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        showToast('保存失败：' + error.message, 'danger');
    })
    .finally(() => {
        saveButton.disabled = false;
        saveButton.innerHTML = '<i class="bi bi-check-lg"></i> 保存';
    });
}

</script>
