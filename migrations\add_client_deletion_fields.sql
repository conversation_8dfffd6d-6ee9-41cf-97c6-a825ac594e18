-- 为客户表添加延迟删除相关字段
-- 适用于 MySQL 5.7
-- 执行时间：2025-08-02

-- 添加删除状态字段
ALTER TABLE clients ADD COLUMN delete_status ENUM('active', 'deleting', 'delete_failed', 'deleted') DEFAULT 'active' COMMENT '删除状态：active=正常，deleting=删除中，delete_failed=删除失败，deleted=已删除';

-- 添加删除开始时间
ALTER TABLE clients ADD COLUMN delete_started_at DATETIME NULL COMMENT '删除开始时间';

-- 添加删除进度信息（JSON格式）
ALTER TABLE clients ADD COLUMN delete_progress TEXT NULL COMMENT '删除进度信息，JSON格式存储';

-- 添加删除失败原因
ALTER TABLE clients ADD COLUMN delete_error_message TEXT NULL COMMENT '删除失败时的错误信息';

-- 添加索引以提高查询性能
CREATE INDEX idx_clients_delete_status ON clients(delete_status);
CREATE INDEX idx_clients_delete_started_at ON clients(delete_started_at);

-- 创建删除任务队列表
CREATE TABLE client_deletion_queue (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    status ENUM('queued', 'running', 'completed', 'failed') DEFAULT 'queued' COMMENT '任务状态',
    priority INT DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    total_images INT DEFAULT 0 COMMENT '总图片数量',
    deleted_images INT DEFAULT 0 COMMENT '已删除图片数量',
    total_contents INT DEFAULT 0 COMMENT '总文案数量',
    deleted_contents INT DEFAULT 0 COMMENT '已删除文案数量',
    estimated_duration INT DEFAULT 0 COMMENT '预估耗时（秒）',
    actual_duration INT DEFAULT 0 COMMENT '实际耗时（秒）',
    current_stage VARCHAR(50) DEFAULT 'pending' COMMENT '当前阶段：pending, deleting_images, deleting_contents, deleting_tasks, deleting_client, completed',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT '开始执行时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户删除任务队列表';

-- 创建删除日志表，记录详细的删除过程
CREATE TABLE client_deletion_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    queue_id INT NOT NULL,
    client_id INT NOT NULL,
    log_level ENUM('info', 'warning', 'error') DEFAULT 'info',
    stage VARCHAR(50) NOT NULL COMMENT '删除阶段',
    message TEXT NOT NULL COMMENT '日志消息',
    details JSON NULL COMMENT '详细信息，JSON格式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (queue_id) REFERENCES client_deletion_queue(id) ON DELETE CASCADE,
    INDEX idx_queue_id (queue_id),
    INDEX idx_client_id (client_id),
    INDEX idx_log_level (log_level),
    INDEX idx_stage (stage),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户删除日志表';

-- 输出完成信息
SELECT 'Client deletion tables created successfully' as message;
