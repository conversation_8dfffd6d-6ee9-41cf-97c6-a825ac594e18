<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>{{ client.name }} - 文案审核</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        /* 小红书风格的顶部导航 */
        .mobile-header {
            background: linear-gradient(135deg, #ff2442 0%, #ff6b6b 100%);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(255, 36, 66, 0.3);
        }
        
        .mobile-header h1 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
            text-align: center;
        }
        
        .mobile-header .subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
            text-align: center;
            margin-top: 0.25rem;
        }
        
        /* 统计卡片 */
        .stats-container {
            padding: 1rem;
            background: white;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            text-align: center;
        }
        
        .stat-item {
            padding: 0.75rem;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff2442;
            display: block;
        }
        
        .stat-label {
            font-size: 0.75rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        /* 筛选栏 */
        .filter-bar {
            background: white;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .filter-tabs {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }

        /* 批量操作按钮 - 移动端优化 */
        .filter-actions {
            display: flex;
            justify-content: center;
        }

        .batch-approve-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            white-space: nowrap;
            min-width: 140px;
            justify-content: center;
        }

        .batch-approve-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            background: linear-gradient(135deg, #218838, #1e7e34);
        }

        .batch-approve-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
        }

        .batch-approve-btn i {
            font-size: 1.1rem;
        }
        
        .filter-tab {
            background: #f8f9fa;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            white-space: nowrap;
            color: #666;
            transition: all 0.2s;
        }
        
        .filter-tab.active {
            background: #ff2442;
            color: white;
        }
        
        /* 内容卡片 - 小红书风格 */
        .content-list {
            padding: 0 1rem;
        }
        
        .content-card {
            background: white;
            border-radius: 16px;
            margin-bottom: 1rem;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            transition: transform 0.2s;
        }
        
        .content-card:active {
            transform: scale(0.98);
        }
        
        .content-header {
            padding: 1rem 1rem 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .content-title {
            font-size: 1rem;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin: 0;
            flex: 1;
            margin-right: 0.5rem;
        }
        
        .content-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d1edff;
            color: #0c5460;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .content-body {
            padding: 0 1rem;
        }
        
        .content-text {
            font-size: 0.9rem;
            line-height: 1.5;
            color: #333;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .content-images {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .content-image {
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
        }
        
        .content-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .content-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: #999;
            margin-bottom: 1rem;
        }
        
        .content-actions {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            border-top: 1px solid #f0f0f0;
        }
        
        .action-btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .btn-approve {
            background: #ff2442;
            color: white;
        }
        
        .btn-approve:hover {
            background: #e01e3a;
            color: white;
        }
        
        .btn-reject {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }
        
        .btn-reject:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        /* 加载更多 */
        .load-more {
            text-align: center;
            padding: 2rem 1rem;
        }
        
        .load-more-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            color: #666;
            padding: 0.75rem 2rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #999;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .content-images {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* 模态框样式 */
        .modal-content {
            border-radius: 16px;
            border: none;
        }
        
        .modal-header {
            border-bottom: 1px solid #f0f0f0;
            padding: 1.5rem;
        }
        
        .modal-title {
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .form-control {
            border-radius: 12px;
            border: 1px solid #dee2e6;
            padding: 0.75rem;
        }
        
        .form-control:focus {
            border-color: #ff2442;
            box-shadow: 0 0 0 0.2rem rgba(255, 36, 66, 0.25);
        }
        
        /* 底部安全区域 */
        .safe-area-bottom {
            height: env(safe-area-inset-bottom);
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <!-- 移动端头部 -->
    <div class="mobile-header">
        <h1>{{ client.name }}</h1>
        <div class="subtitle">文案审核</div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-container">
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number" id="totalCount">-</span>
                <div class="stat-label">总文案</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="pendingCount">-</span>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="reviewedCount">-</span>
                <div class="stat-label">已审核</div>
            </div>
        </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
        <div class="filter-tabs">
            <button class="filter-tab active" data-status="">全部</button>
            <button class="filter-tab" data-status="pending">待审核</button>
            <button class="filter-tab" data-status="approved">待发布</button>
            <button class="filter-tab" data-status="published">已发布</button>
        </div>
        <div class="filter-actions">
            <button class="batch-approve-btn" onclick="batchApproveAll()" title="一键通过所有待审核文案">
                <i class="bi bi-check-all"></i>
                <span>全部通过</span>
            </button>
        </div>
    </div>

    <!-- 内容列表 -->
    <div class="content-list" id="contentList">
        <!-- 内容将通过JavaScript动态加载 -->
    </div>

    <!-- 加载更多 -->
    <div class="load-more" id="loadMore" style="display: none;">
        <button class="load-more-btn" onclick="loadMoreContent()">
            <i class="bi bi-arrow-clockwise me-2"></i>加载更多
        </button>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" id="emptyState" style="display: none;">
        <i class="bi bi-inbox"></i>
        <div>暂无文案内容</div>
    </div>

    <!-- 驳回理由模态框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">驳回理由</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">请选择或输入驳回理由：</label>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('内容质量不符合要求，需要重新编写')">内容质量不达标</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('标题不够吸引人，建议重新设计')">标题需要优化</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('图片质量需要提升，建议更换高质量图片')">图片质量问题</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm quick-reason"
                                    onclick="fillRejectReason('文案风格与品牌调性不符')">风格不符</button>
                        </div>
                        <textarea class="form-control" id="rejectReason" rows="4"
                                  placeholder="请输入具体的驳回理由..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">确认驳回</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        let currentStatus = '';
        let currentContentId = null;
        let isLoading = false;
        let hasMore = true;
        let allContents = []; // 存储所有文案数据
        const shareKey = '{{ share_key }}';

        // 调试信息
        console.log('ShareKey from template:', shareKey);
        if (!shareKey || shareKey === 'undefined') {
            console.error('ShareKey is undefined or empty!');
            alert('页面加载错误：分享密钥未找到');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadContent();
            initFilterTabs();
        });

        // 初始化筛选标签
        function initFilterTabs() {
            const tabs = document.querySelectorAll('.filter-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 更新激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 重置并加载新内容
                    currentStatus = this.dataset.status;
                    currentPage = 1;
                    hasMore = true;
                    document.getElementById('contentList').innerHTML = '';
                    loadContent();
                });
            });
        }

        // 获取访问密钥
        function getAccessKey() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('key');
        }

        // 构建API URL
        function buildApiUrl(path) {
            const accessKey = getAccessKey();
            const url = `/client-review/api/${shareKey}${path}`;
            if (accessKey) {
                const separator = path.includes('?') ? '&' : '?';
                return `${url}${separator}key=${accessKey}`;
            }
            return url;
        }

        // 加载统计信息
        function loadStats() {
            fetch(buildApiUrl('/stats'))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        document.getElementById('totalCount').textContent = stats.total_count;
                        document.getElementById('pendingCount').textContent = stats.pending_count;
                        document.getElementById('reviewedCount').textContent = stats.reviewed_count;
                    }
                })
                .catch(error => {
                    console.error('加载统计信息失败:', error);
                });
        }

        // 加载内容列表
        function loadContent() {
            if (isLoading || !hasMore) return;

            isLoading = true;
            const params = new URLSearchParams({
                page: currentPage,
                per_page: 10,
                status: currentStatus
            });

            fetch(buildApiUrl(`/contents?${params}`))
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const contents = data.contents;

                        // 更新全局文案数据
                        if (currentPage === 1) {
                            allContents = contents; // 第一页时重置
                        } else {
                            allContents = allContents.concat(contents); // 后续页面追加
                        }

                        if (contents.length === 0) {
                            if (currentPage === 1) {
                                showEmptyState();
                            } else {
                                hasMore = false;
                                hideLoadMore();
                            }
                        } else {
                            renderContents(contents);
                            currentPage++;

                            // 检查是否还有更多内容
                            if (contents.length < 10) {
                                hasMore = false;
                                hideLoadMore();
                            } else {
                                showLoadMore();
                            }
                        }
                    } else {
                        showError('加载内容失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('加载内容失败:', error);
                    showError('网络错误，请稍后重试');
                })
                .finally(() => {
                    isLoading = false;
                });
        }

        // 渲染内容列表
        function renderContents(contents) {
            const container = document.getElementById('contentList');

            contents.forEach(content => {
                const contentCard = createContentCard(content);
                container.appendChild(contentCard);
            });

            hideEmptyState();
        }

        // 创建内容卡片
        function createContentCard(content) {
            const card = document.createElement('div');
            card.className = 'content-card';

            // 添加点击事件跳转到详情页
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮，不跳转
                if (e.target.closest('.action-btn')) {
                    return;
                }
                const accessKey = getAccessKey();
                let detailUrl = `/client-review/${shareKey}/content/${content.id}`;
                if (accessKey) {
                    detailUrl += `?key=${accessKey}`;
                }
                window.location.href = detailUrl;
            });

            // 根据工作流状态和客户审核状态确定显示状态
            function getDisplayStatus(content) {
                // 已发布
                if (content.publish_status === 'published') {
                    return { class: 'status-published', text: '已发布' };
                }

                // 待发布：客户审核通过（包括手动和自动）且工作流状态为ready_to_publish或pending_publish
                if ((content.client_review_status === 'approved' || content.client_review_status === 'auto_approved') &&
                    (content.workflow_status === 'ready_to_publish' || content.workflow_status === 'pending_publish')) {
                    return { class: 'status-approved', text: '待发布' };
                }

                // 待审核：其他情况
                return { class: 'status-pending', text: '待审核' };
            }

            const status = getDisplayStatus(content);

            // 构建图片网格
            let imagesHtml = '';
            if (content.images && content.images.length > 0) {
                imagesHtml = '<div class="content-images">';
                content.images.slice(0, 6).forEach(image => {
                    imagesHtml += `
                        <div class="content-image">
                            <img src="${image.url}" alt="文案图片" loading="lazy">
                        </div>
                    `;
                });
                imagesHtml += '</div>';
            }

            // 构建操作按钮
            let actionsHtml = '';
            if (content.client_review_status === 'pending_client_review') {
                actionsHtml = `
                    <div class="content-actions">
                        <button class="action-btn btn-approve" onclick="approveContent(${content.id})">
                            <i class="bi bi-check-lg me-1"></i>通过
                        </button>
                        <button class="action-btn btn-reject" onclick="showRejectModal(${content.id})">
                            <i class="bi bi-x-lg me-1"></i>驳回
                        </button>
                    </div>
                `;
            }

            card.innerHTML = `
                <div class="content-header">
                    <h3 class="content-title">${content.title || '无标题'}</h3>
                    <span class="content-status ${status.class}">${status.text}</span>
                </div>
                <div class="content-body">
                    <div class="content-text">${content.content || ''}</div>
                    ${imagesHtml}
                    <div class="content-meta">
                        <span>创建时间: ${formatDate(content.created_at)}</span>
                        <span>任务: ${content.task_name || '默认任务'}</span>
                    </div>
                </div>
                ${actionsHtml}
            `;

            return card;
        }

        // 显示驳回模态框
        function showRejectModal(contentId) {
            currentContentId = contentId;
            document.getElementById('rejectReason').value = '';
            const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
            modal.show();
        }

        // 填充快捷驳回理由
        function fillRejectReason(reason) {
            const textarea = document.getElementById('rejectReason');
            const currentValue = textarea.value.trim();
            if (currentValue) {
                textarea.value = currentValue + '\n' + reason;
            } else {
                textarea.value = reason;
            }
        }

        // 通过审核
        function approveContent(contentId) {
            if (!confirm('确定要通过这篇文案吗？')) return;

            reviewContent(contentId, 'approve', '');
        }

        // 批量通过所有待审核文案
        function batchApproveAll() {
            // 从页面上的文案卡片中获取待审核文案ID
            const contentCards = document.querySelectorAll('.content-card');
            const pendingContentIds = [];

            contentCards.forEach(card => {
                // 检查是否有通过按钮（只有待审核状态才有通过按钮）
                const approveBtn = card.querySelector('.btn-approve');
                if (approveBtn) {
                    // 从通过按钮的onclick属性中提取content_id
                    const onclickAttr = approveBtn.getAttribute('onclick');
                    const match = onclickAttr.match(/approveContent\((\d+)\)/);
                    if (match) {
                        pendingContentIds.push(parseInt(match[1]));
                    }
                }
            });

            if (pendingContentIds.length === 0) {
                alert('当前页面没有待审核的文案');
                return;
            }

            if (!confirm(`确定要通过全部 ${pendingContentIds.length} 篇待审核文案吗？此操作不可撤销。`)) {
                return;
            }

            // 创建文案对象数组
            const pendingContents = pendingContentIds.map(id => ({ id: id }));

            const totalCount = pendingContents.length;
            const contentIds = pendingContents.map(content => content.id);

            // 显示进度提示
            alert(`正在批量通过 ${totalCount} 篇文案...`);

            // 调用批量审核API
            fetch(buildApiUrl('/contents/batch-approve'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    content_ids: contentIds
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);

                    // 刷新页面数据
                    setTimeout(() => {
                        loadContent();
                        loadStats();
                    }, 1000);
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('批量审核失败:', error);
                alert('网络错误，请稍后重试');
            });
        }

        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            if (!reason) {
                alert('请输入驳回理由');
                return;
            }

            reviewContent(currentContentId, 'reject', reason);
            bootstrap.Modal.getInstance(document.getElementById('rejectModal')).hide();
        }

        // 审核内容
        function reviewContent(contentId, action, comment, rejectionType = null, callback = null) {
            const formData = new FormData();
            formData.append('action', action);
            formData.append('review_comment', comment);

            // 如果是驳回操作，添加驳回类型
            if (action === 'reject' && rejectionType) {
                formData.append('rejection_type', rejectionType);
            }

            fetch(buildApiUrl(`/contents/${contentId}/review`), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (!callback) {
                        showToast(data.message, 'success');
                        // 重新加载当前页面内容
                        refreshCurrentContent();
                        // 更新统计信息
                        loadStats();
                    }

                    // 执行回调函数
                    if (callback) {
                        callback(true, data.message);
                    }
                } else {
                    if (!callback) {
                        showToast(data.message, 'error');
                    }

                    // 执行回调函数
                    if (callback) {
                        callback(false, data.message);
                    }
                }
            })
            .catch(error => {
                console.error('审核失败:', error);
                if (!callback) {
                    showToast('网络错误，请稍后重试', 'error');
                }

                // 执行回调函数
                if (callback) {
                    callback(false, '网络错误，请稍后重试');
                }
            });
        }

        // 刷新当前内容
        function refreshCurrentContent() {
            currentPage = 1;
            hasMore = true;
            document.getElementById('contentList').innerHTML = '';
            loadContent();
        }

        // 加载更多内容
        function loadMoreContent() {
            loadContent();
        }

        // 显示/隐藏加载更多按钮
        function showLoadMore() {
            document.getElementById('loadMore').style.display = 'block';
        }

        function hideLoadMore() {
            document.getElementById('loadMore').style.display = 'none';
        }

        // 显示/隐藏空状态
        function showEmptyState() {
            document.getElementById('emptyState').style.display = 'block';
            hideLoadMore();
        }

        function hideEmptyState() {
            document.getElementById('emptyState').style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            showToast(message, 'error');
        }

        // 显示提示信息
        function showToast(message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toastId = 'toast-' + Date.now();
            const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-primary';
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { autohide: true, delay: 3000 });
            toast.show();

            // 自动清理
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    </script>
</body>
</html>
