<!-- 发布管理客户详情页面 -->

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="bi bi-send"></i> 发布管理 - {{ client.name }}</h2>
                <a href="{{ url_for('main_simple.publish_manage') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> 返回客户列表
                </a>
            </div>
        </div>
    </div>

    <!-- 筛选功能 -->
    <div class="card mb-3">
        <div class="card-body">
            <form id="filterForm" method="GET" action="{{ url_for('main_simple.publish_manage_client_detail', client_id=client.id) }}">
                <input type="hidden" name="sort_by" value="{{ request.args.get('sort_by', '') }}">
                <div class="row g-2 align-items-center">
                    <div class="col-auto">
                        <label class="form-label mb-0"><i class="bi bi-funnel"></i> 筛选条件:</label>
                    </div>
                    <div class="col-auto">
                        <label for="title" class="form-label mb-0 me-2">标题:</label>
                        <input type="text" class="form-control form-control-sm d-inline-block" id="title" name="title"
                               value="{{ request.args.get('title', '') }}"
                               placeholder="输入标题关键词" style="width: 200px;">
                    </div>
                    <div class="col-auto">
                        <label for="priority" class="form-label mb-0 me-2">优先级:</label>
                        <select class="form-select form-select-sm d-inline-block" id="priority" name="priority" style="width: 120px;">
                            <option value="">全部</option>
                            <option value="high" {% if request.args.get('priority') == 'high' %}selected{% endif %}>高</option>
                            <option value="normal" {% if request.args.get('priority') == 'normal' %}selected{% endif %}>普通</option>
                            <option value="low" {% if request.args.get('priority') == 'low' %}selected{% endif %}>低</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="bi bi-search"></i> 筛选
                        </button>
                        <a href="{{ url_for('main_simple.publish_manage_client_detail', client_id=client.id) }}" class="btn btn-outline-secondary btn-sm ms-1">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 排序和批量操作按钮 -->
    <div class="mb-2 d-flex justify-content-between align-items-center">
        <div>
            <span class="text-muted me-2">排序:</span>
            <div class="btn-group btn-group-sm me-3" role="group">
                <button type="button" class="btn btn-outline-info sort-btn {% if request.args.get('sort_by') == 'review_time_asc' %}active{% endif %}"
                        data-sort="review_time_asc" title="审核日期正序">
                    <i class="bi bi-calendar-check"></i> 审核日期 <i class="bi bi-sort-up"></i>
                </button>
                <button type="button" class="btn btn-outline-info sort-btn {% if request.args.get('sort_by') == 'review_time_desc' %}active{% endif %}"
                        data-sort="review_time_desc" title="审核日期倒序">
                    <i class="bi bi-calendar-check"></i> 审核日期 <i class="bi bi-sort-down"></i>
                </button>
            </div>
            <div class="btn-group btn-group-sm me-3" role="group">
                <button type="button" class="btn btn-outline-warning sort-btn {% if request.args.get('sort_by') == 'priority_asc' %}active{% endif %}"
                        data-sort="priority_asc" title="优先级正序(低到高)">
                    <i class="bi bi-flag"></i> 优先级 <i class="bi bi-sort-up"></i>
                </button>
                <button type="button" class="btn btn-outline-warning sort-btn {% if request.args.get('sort_by') == 'priority_desc' %}active{% endif %}"
                        data-sort="priority_desc" title="优先级倒序(高到低)">
                    <i class="bi bi-flag"></i> 优先级 <i class="bi bi-sort-down"></i>
                </button>
            </div>
            <button type="button" class="btn btn-outline-secondary btn-sm sort-btn {% if not request.args.get('sort_by') or request.args.get('sort_by') == 'default' %}active{% endif %}"
                    data-sort="default" title="默认排序">
                <i class="bi bi-arrow-clockwise"></i> 默认
            </button>
        </div>
        <div>
            <button class="btn btn-primary btn-sm" id="batch-submit-btn">批量提交发布</button>
            <button class="btn btn-danger btn-sm" id="batch-delete-btn">批量删除</button>
            <button class="btn btn-secondary btn-sm" id="batch-priority-btn">批量设置优先级</button>
        </div>
    </div>

    <!-- 文案列表 -->
    <div class="card">
        <div class="card-body">
            {% if content_data %}
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th style="width: 40px;"><input type="checkbox" id="select-all"></th>
                            <th style="width: 60px;">ID</th>
                            <th>标题</th>
                            <th>图片</th>
                            <th style="width: 120px;">审核日期</th>
                            <th>优先级</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in content_data %}
                        <tr>
                            <td><input type="checkbox" class="content-checkbox" value="{{ item.content.id }}"></td>
                            <td><small class="text-muted">{{ item.content.id }}</small></td>
                            <td>{{ item.content.title }}</td>
                            <td>
                                {% if item.images %}
                                    <span class="badge bg-primary">{{ item.image_count }} 张图片</span>
                                {% else %}
                                    <span class="text-muted">无图片</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.content.review_time %}
                                    <small class="text-muted">{{ item.content.review_time.strftime('%m-%d %H:%M') }}</small>
                                {% else %}
                                    <small class="text-muted">-</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if item.content.publish_priority == 'high' %}
                                    <span class="badge bg-danger">高</span>
                                {% elif item.content.publish_priority == 'normal' %}
                                    <span class="badge bg-warning">普通</span>
                                {% elif item.content.publish_priority == 'low' %}
                                    <span class="badge bg-secondary">低</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">未设置</span>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-primary ms-1" onclick="setPriority({{ item.content.id }})" title="设置优先级">
                                    <i class="bi bi-gear"></i>
                                </button>
                            </td>
                            <td>
                                <button class="btn btn-info btn-sm" onclick="previewContent({{ item.content.id }})" title="预览">
                                    <i class="bi bi-eye"></i> 预览
                                </button>
                                <button class="btn btn-primary btn-sm" onclick="submitForPublish({{ item.content.id }})">提交发布</button>
                                <button class="btn btn-danger btn-sm ms-1" onclick="deleteContent({{ item.content.id }})">删除</button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-send text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无待发布的文案</h4>
                    <p class="text-muted">该客户暂无需要发布的文案</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <nav class="mt-3">
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main_simple.publish_manage_client_detail', client_id=client.id, page=pagination.prev_num, title=request.args.get('title', ''), priority=request.args.get('priority', ''), sort_by=request.args.get('sort_by', '')) }}">上一页</a>
            </li>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('main_simple.publish_manage_client_detail', client_id=client.id, page=page_num, title=request.args.get('title', ''), priority=request.args.get('priority', ''), sort_by=request.args.get('sort_by', '')) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main_simple.publish_manage_client_detail', client_id=client.id, page=pagination.next_num, title=request.args.get('title', ''), priority=request.args.get('priority', ''), sort_by=request.args.get('sort_by', '')) }}">下一页</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- 优先级设置模态框 -->
<div class="modal fade" id="priorityModal" tabindex="-1" aria-labelledby="priorityModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="priorityModalLabel">设置发布优先级</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>请选择发布优先级：</p>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-danger priority-tag" data-priority="high">
                        <span class="badge bg-danger">高</span> 优先发布
                    </button>
                    <button type="button" class="btn btn-outline-warning priority-tag" data-priority="normal">
                        <span class="badge bg-warning">普通</span> 正常发布
                    </button>
                    <button type="button" class="btn btn-outline-secondary priority-tag" data-priority="low">
                        <span class="badge bg-secondary">低</span> 延后发布
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<!-- 文案预览模态框 -->
<div class="modal fade" id="contentPreviewModal" tabindex="-1" aria-labelledby="contentPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentPreviewModalLabel">
                    <i class="bi bi-eye"></i> 文案预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contentPreviewBody">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载文案详情...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 图片预览模态框 -->
<div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imagePreviewModalLabel">
                    <i class="bi bi-image"></i> 图片预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="previewImage" src="" class="img-fluid" style="max-height: 70vh;">
            </div>
            <div class="modal-footer justify-content-center">
                <span id="imageInfo" class="text-muted"></span>
            </div>
        </div>
    </div>
</div>

<script>
// 全选功能
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.content-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

// 排序功能
document.querySelectorAll('.sort-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const sortBy = this.getAttribute('data-sort');

        // 更新按钮状态
        document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');

        // 构建新的URL参数
        const url = new URL(window.location);
        if (sortBy === 'default') {
            url.searchParams.delete('sort_by');
        } else {
            url.searchParams.set('sort_by', sortBy);
        }

        // 跳转到新URL
        window.location.href = url.toString();
    });
});

// 提交发布单个文案
function submitForPublish(contentId) {
    fetch(`/simple/api/submit-for-publish/${contentId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('提交发布成功', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast('提交发布失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('提交发布失败，请重试', 'error');
    });
}

// 显示右上角提示
function showToast(message, type = 'success') {
    // 移除已存在的提示
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast-notification alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    toast.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // 3秒后自动消失
    setTimeout(() => {
        if (toast && toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

// 删除单个文案
function deleteContent(contentId) {
    // 使用Bootstrap模态框确认
    const confirmModal = document.createElement('div');
    confirmModal.className = 'modal fade';
    confirmModal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle text-warning"></i> 确认删除
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-0">确定要删除这篇文案吗？删除后无法恢复。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="bi bi-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(confirmModal);
    const modal = new bootstrap.Modal(confirmModal);
    modal.show();

    // 确认删除按钮事件
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        modal.hide();

        // 执行删除
        fetch(`/simple/api/publish-manage/delete/${contentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('文案删除成功', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast('删除失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            showToast('删除失败，请重试', 'error');
        });
    });

    // 模态框关闭后移除DOM元素
    confirmModal.addEventListener('hidden.bs.modal', function() {
        confirmModal.remove();
    });
}

// 当前选中的文案ID和优先级
let currentContentId = null;
let currentPriority = null;

// 批量提交发布
document.getElementById('batch-submit-btn').addEventListener('click', function() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showToast('请选择要提交发布的文案', 'error');
        return;
    }

    const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    fetch('/simple/api/batch-submit-for-publish', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ content_ids: contentIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`批量提交发布成功！${data.submitted_count} 篇文案已进入发布队列`, 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showToast('批量提交发布失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('批量提交发布失败，请重试', 'error');
    });
});

// 批量删除
document.getElementById('batch-delete-btn').addEventListener('click', function() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showToast('请选择要删除的文案', 'error');
        return;
    }

    // 使用Bootstrap模态框确认
    const confirmModal = document.createElement('div');
    confirmModal.className = 'modal fade';
    confirmModal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle text-warning"></i> 确认批量删除
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-2">确定要删除选中的 <strong>${selectedCheckboxes.length}</strong> 篇文案吗？</p>
                    <p class="mb-0 text-danger"><i class="bi bi-exclamation-circle"></i> 删除后无法恢复，请谨慎操作。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmBatchDeleteBtn">
                        <i class="bi bi-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(confirmModal);
    const modal = new bootstrap.Modal(confirmModal);
    modal.show();

    // 确认删除按钮事件
    document.getElementById('confirmBatchDeleteBtn').addEventListener('click', function() {
        modal.hide();

        const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        fetch('/simple/api/publish-manage/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ content_ids: contentIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`批量删除成功！${data.deleted_count} 篇文案已删除`, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast('批量删除失败：' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('批量删除失败，请重试', 'error');
        });
    });

    // 模态框关闭后移除DOM元素
    confirmModal.addEventListener('hidden.bs.modal', function() {
        confirmModal.remove();
    });
});

// 批量设置优先级
document.getElementById('batch-priority-btn').addEventListener('click', function() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showToast('请选择要设置优先级的文案', 'error');
        return;
    }

    // 显示批量优先级设置模态框
    showBatchPriorityModal(selectedCheckboxes);
});

// 显示批量优先级设置模态框
function showBatchPriorityModal(selectedCheckboxes) {
    // 重置所有标签状态
    document.querySelectorAll('.priority-tag').forEach(tag => {
        tag.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
        const priority = tag.dataset.priority;
        if (priority === 'high') {
            tag.classList.add('btn-outline-danger');
        } else if (priority === 'normal') {
            tag.classList.add('btn-outline-warning');
        } else if (priority === 'low') {
            tag.classList.add('btn-outline-secondary');
        }
    });

    // 设置默认选中普通优先级
    const defaultTag = document.querySelector('[data-priority="normal"]');
    if (defaultTag) {
        defaultTag.classList.remove('btn-outline-warning');
        defaultTag.classList.add('btn-warning', 'active');
    }

    // 更新模态框标题
    const modalTitle = document.getElementById('priorityModalLabel');
    modalTitle.textContent = `批量设置优先级 (${selectedCheckboxes.length} 篇文案)`;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('priorityModal'));
    modal.show();

    // 移除之前的事件监听器，避免重复绑定
    document.querySelectorAll('.priority-tag').forEach(tag => {
        tag.replaceWith(tag.cloneNode(true));
    });

    // 重新绑定事件监听器
    document.querySelectorAll('.priority-tag').forEach(tag => {
        tag.addEventListener('click', function() {
            const priority = this.dataset.priority;

            // 更新标签样式
            document.querySelectorAll('.priority-tag').forEach(t => {
                t.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
                const p = t.dataset.priority;
                if (p === 'high') {
                    t.classList.add('btn-outline-danger');
                } else if (p === 'normal') {
                    t.classList.add('btn-outline-warning');
                } else if (p === 'low') {
                    t.classList.add('btn-outline-secondary');
                }
            });

            this.classList.remove('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
            if (priority === 'high') {
                this.classList.add('btn-danger');
            } else if (priority === 'normal') {
                this.classList.add('btn-warning');
            } else if (priority === 'low') {
                this.classList.add('btn-secondary');
            }
            this.classList.add('active');

            // 批量提交优先级设置
            submitBatchPriority(priority, selectedCheckboxes);
        });
    });
}

// 批量提交优先级设置
function submitBatchPriority(priority, selectedCheckboxes) {
    const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    fetch('/simple/api/publish/batch-priority', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            content_ids: contentIds,
            priority: priority
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('priorityModal'));
            modal.hide();
            // 批量更新页面上的优先级显示
            contentIds.forEach(contentId => {
                updatePriorityDisplay(contentId, priority);
            });
            // 取消所有选中状态
            document.querySelectorAll('.content-checkbox:checked').forEach(cb => cb.checked = false);
            document.getElementById('select-all').checked = false;
        } else {
            showToast('批量设置优先级失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('批量设置优先级失败，请重试', 'error');
    });
}

// 显示单个优先级设置模态框
function setPriority(contentId) {
    currentContentId = contentId;

    // 获取当前文案的优先级
    const row = document.querySelector(`input[value="${contentId}"]`).closest('tr');
    const priorityBadge = row.querySelector('td:nth-child(6) .badge');
    if (priorityBadge.textContent.includes('高')) {
        currentPriority = 'high';
    } else if (priorityBadge.textContent.includes('普通')) {
        currentPriority = 'normal';
    } else if (priorityBadge.textContent.includes('低')) {
        currentPriority = 'low';
    } else {
        currentPriority = 'normal'; // 默认普通
    }

    // 重置所有标签状态
    document.querySelectorAll('.priority-tag').forEach(tag => {
        tag.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
        const priority = tag.dataset.priority;
        if (priority === 'high') {
            tag.classList.add('btn-outline-danger');
        } else if (priority === 'normal') {
            tag.classList.add('btn-outline-warning');
        } else if (priority === 'low') {
            tag.classList.add('btn-outline-secondary');
        }
    });

    // 设置当前优先级为选中状态
    const currentTag = document.querySelector(`[data-priority="${currentPriority}"]`);
    if (currentTag) {
        currentTag.classList.remove('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
        if (currentPriority === 'high') {
            currentTag.classList.add('btn-danger');
        } else if (currentPriority === 'normal') {
            currentTag.classList.add('btn-warning');
        } else if (currentPriority === 'low') {
            currentTag.classList.add('btn-secondary');
        }
        currentTag.classList.add('active');
    }

    // 更新模态框标题
    const modalTitle = document.getElementById('priorityModalLabel');
    modalTitle.textContent = '设置发布优先级';

    const modal = new bootstrap.Modal(document.getElementById('priorityModal'));
    modal.show();

    // 移除之前的事件监听器，避免重复绑定
    document.querySelectorAll('.priority-tag').forEach(tag => {
        tag.replaceWith(tag.cloneNode(true));
    });

    // 重新绑定事件监听器
    document.querySelectorAll('.priority-tag').forEach(tag => {
        tag.addEventListener('click', function() {
            const priority = this.dataset.priority;

            // 更新标签样式
            document.querySelectorAll('.priority-tag').forEach(t => {
                t.classList.remove('active', 'btn-danger', 'btn-warning', 'btn-secondary');
                const p = t.dataset.priority;
                if (p === 'high') {
                    t.classList.add('btn-outline-danger');
                } else if (p === 'normal') {
                    t.classList.add('btn-outline-warning');
                } else if (p === 'low') {
                    t.classList.add('btn-outline-secondary');
                }
            });

            this.classList.remove('btn-outline-danger', 'btn-outline-warning', 'btn-outline-secondary');
            if (priority === 'high') {
                this.classList.add('btn-danger');
            } else if (priority === 'normal') {
                this.classList.add('btn-warning');
            } else if (priority === 'low') {
                this.classList.add('btn-secondary');
            }
            this.classList.add('active');

            // 提交优先级设置
            submitPriority(priority);
        });
    });
}

// 提交单个优先级设置
function submitPriority(priority) {
    if (!currentContentId) {
        return;
    }

    fetch(`/simple/api/publish/${currentContentId}/priority`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ priority: priority })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 直接更新页面上的优先级显示，不刷新页面
            updatePriorityDisplay(currentContentId, priority);
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('priorityModal'));
            modal.hide();
        } else {
            showToast('设置失败：' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('设置失败，请重试', 'error');
    });
}

// 更新页面上的优先级显示
function updatePriorityDisplay(contentId, priority) {
    const row = document.querySelector(`input[value="${contentId}"]`).closest('tr');
    const priorityCell = row.querySelector('td:nth-child(6)'); // 优先级列

    let badgeHtml = '';
    if (priority === 'high') {
        badgeHtml = '<span class="badge bg-danger">高</span>';
    } else if (priority === 'normal') {
        badgeHtml = '<span class="badge bg-warning">普通</span>';
    } else if (priority === 'low') {
        badgeHtml = '<span class="badge bg-secondary">低</span>';
    } else {
        badgeHtml = '<span class="badge bg-light text-dark">未设置</span>';
    }

    // 重新创建设置按钮，保持原有的onclick事件
    const buttonHtml = `<button class="btn btn-sm btn-outline-primary ms-1" onclick="setPriority(${contentId})" title="设置优先级">
                            <i class="bi bi-gear"></i>
                        </button>`;

    priorityCell.innerHTML = badgeHtml + buttonHtml;
}

// 预览文案内容
function previewContent(contentId) {
    console.log('预览文案，ID:', contentId);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('contentPreviewModal'));
    modal.show();

    // 重置模态框内容为加载状态
    const modalBody = document.getElementById('contentPreviewBody');
    modalBody.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载文案详情...</p>
        </div>
    `;

    // 获取文案详情
    fetch(`/simple/api/contents/${contentId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                renderContentPreview(data);
            } else {
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        加载失败: ${data.message || '未知错误'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('加载文案详情失败:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    网络错误，请稍后重试: ${error.message}
                </div>
            `;
        });
}

// 渲染文案预览内容
function renderContentPreview(data) {
    const modalBody = document.getElementById('contentPreviewBody');
    const content = data.content;

    // 格式化状态显示
    const statusMap = {
        'draft': { text: '草稿', class: 'secondary' },
        'first_reviewed': { text: '初审通过', class: 'info' },
        'pending_final_review': { text: '待最终审核', class: 'warning' },
        'client_approved': { text: '客户已通过', class: 'success' },
        'ready_to_publish': { text: '准备发布', class: 'primary' },
        'pending_publish': { text: '待发布', class: 'info' },
        'published': { text: '已发布', class: 'success' }
    };

    const status = statusMap[content.workflow_status] || { text: content.workflow_status, class: 'secondary' };

    modalBody.innerHTML = `
        <div class="row">
            <!-- 左侧：文案内容和信息 -->
            <div class="col-md-7">
                <!-- 文案标题和内容 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-file-text"></i> ${content.title || '无标题'}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>文案内容：</strong>
                            <div class="border rounded p-3 mt-2" style="background-color: #f8f9fa; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">${content.content || '暂无内容'}</div>
                        </div>
                    </div>
                </div>

                <!-- 扩展信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-tags"></i> 扩展信息
                        </h6>
                    </div>
                    <div class="card-body">
                        ${content.topics && content.topics.length > 0 ? `
                        <div class="mb-3">
                            <strong>话题标签：</strong>
                            <div class="mt-1">
                                ${content.topics.map(topic => `<span class="badge bg-info me-1">#${topic}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                        ${content.at_users && content.at_users.length > 0 ? `
                        <div class="mb-3">
                            <strong>@用户：</strong>
                            <div class="mt-1">
                                ${content.at_users.map(user => `<span class="badge bg-warning me-1">@${user}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                        ${content.location ? `
                        <div class="mb-3">
                            <strong>定位：</strong>
                            <div class="mt-1">
                                <span class="badge bg-success"><i class="bi bi-geo-alt"></i> ${content.location}</span>
                            </div>
                        </div>
                        ` : ''}
                        ${(!content.topics || content.topics.length === 0) && (!content.at_users || content.at_users.length === 0) && !content.location ? `
                        <div class="text-muted text-center py-3">
                            <i class="bi bi-info-circle"></i> 暂无扩展信息
                        </div>
                        ` : ''}
                    </div>
                </div>

                <!-- 基本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle"></i> 基本信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="mb-2">
                                    <strong>ID：</strong>
                                    <span class="text-muted">${content.id}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>客户：</strong>
                                    <span class="text-muted">${content.client_name || '未知'}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>状态：</strong>
                                    <span class="badge bg-${status.class}">${status.text}</span>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="mb-2">
                                    <strong>创建时间：</strong><br>
                                    <small class="text-muted">${content.created_at}</small>
                                </div>
                                ${content.review_time ? `
                                <div class="mb-2">
                                    <strong>审核时间：</strong><br>
                                    <small class="text-muted">${content.review_time}</small>
                                </div>
                                ` : ''}
                                ${content.publish_priority ? `
                                <div class="mb-2">
                                    <strong>发布优先级：</strong>
                                    <span class="badge bg-${content.publish_priority === 'high' ? 'danger' : content.publish_priority === 'normal' ? 'warning' : 'secondary'}">${content.publish_priority === 'high' ? '高' : content.publish_priority === 'normal' ? '普通' : '低'}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：图片区域 -->
            <div class="col-md-5">
                <div class="card" id="contentImagesCard" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-images"></i> 配图
                        </h6>
                    </div>
                    <div class="card-body" id="contentImagesBody">
                        <!-- 图片将通过AJAX加载 -->
                    </div>
                </div>
                <div id="noImagesPlaceholder" class="card">
                    <div class="card-body text-center text-muted">
                        <i class="bi bi-image" style="font-size: 3rem;"></i>
                        <p class="mt-2">暂无配图</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 异步加载图片
    loadContentImages(content.id);
}

// 加载文案图片
function loadContentImages(contentId) {
    console.log('开始加载图片，contentId:', contentId);

    fetch(`/simple/api/images/${contentId}`)
        .then(response => response.json())
        .then(data => {
            const imagesCard = document.getElementById('contentImagesCard');
            const imagesBody = document.getElementById('contentImagesBody');
            const noImagesPlaceholder = document.getElementById('noImagesPlaceholder');

            if (data.success && data.images && data.images.length > 0) {
                if (imagesCard && imagesBody) {
                    // 显示图片卡片，隐藏占位符
                    imagesCard.style.display = 'block';
                    if (noImagesPlaceholder) {
                        noImagesPlaceholder.style.display = 'none';
                    }

                    // 渲染图片 - 使用更窄的缩略图
                    const imagesHtml = `
                        <div class="row g-1">
                            ${data.images.map((image, index) => `
                                <div class="col-4">
                                    <div class="position-relative">
                                        <img src="${image.image_url || image.file_path}"
                                             class="img-thumbnail w-100"
                                             style="height: 80px; object-fit: cover; cursor: pointer;"
                                             onclick="showImagePreview('${image.image_url || image.file_path}', ${index + 1}, ${data.images.length})"
                                             title="点击查看大图 (${index + 1}/${data.images.length})">
                                        <span class="position-absolute top-0 end-0 badge bg-primary" style="font-size: 0.6rem;">${index + 1}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        <div class="mt-2 text-center">
                            <small class="text-muted">共 ${data.images.length} 张图片，点击查看大图</small>
                        </div>
                    `;

                    imagesBody.innerHTML = imagesHtml;
                }
            } else {
                // 没有图片时显示占位符
                if (imagesCard) {
                    imagesCard.style.display = 'none';
                }
                if (noImagesPlaceholder) {
                    noImagesPlaceholder.style.display = 'block';
                }
            }
        })
        .catch(error => {
            console.error('加载图片失败:', error);
        });
}

// 显示图片预览
function showImagePreview(imageUrl, currentIndex, totalCount) {
    const modal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
    const previewImage = document.getElementById('previewImage');
    const imageInfo = document.getElementById('imageInfo');

    previewImage.src = imageUrl;
    imageInfo.textContent = `第 ${currentIndex} 张，共 ${totalCount} 张`;

    modal.show();
}

console.log('发布管理客户详情页面已加载');
</script>
