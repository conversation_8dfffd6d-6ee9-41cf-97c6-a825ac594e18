-- MySQL dump 10.13  Distrib 5.7.26, for Win64 (x86_64)
--
-- Host: localhost    Database: xhsrw666
-- ------------------------------------------------------
-- Server version	5.7.26

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `alembic_version`
--

DROP TABLE IF EXISTS `alembic_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `alembic_version` (
  `version_num` varchar(32) NOT NULL,
  PRIMARY KEY (`version_num`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alembic_version`
--

LOCK TABLES `alembic_version` WRITE;
/*!40000 ALTER TABLE `alembic_version` DISABLE KEYS */;
INSERT INTO `alembic_version` VALUES ('add_performance_indexes'),('f53868527c9d');
/*!40000 ALTER TABLE `alembic_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `batches`
--

DROP TABLE IF EXISTS `batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `content_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前批次中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `batches_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `batches_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `batches`
--

LOCK TABLES `batches` WRITE;
/*!40000 ALTER TABLE `batches` DISABLE KEYS */;
INSERT INTO `batches` VALUES (39,21,'批次1',5,'2025-07-31 18:09:33',1,0);
/*!40000 ALTER TABLE `batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_mark_defaults`
--

DROP TABLE IF EXISTS `client_mark_defaults`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_mark_defaults` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `mark_name` varchar(100) NOT NULL,
  `default_values` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_client_mark` (`client_id`,`mark_name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_mark_defaults`
--

LOCK TABLES `client_mark_defaults` WRITE;
/*!40000 ALTER TABLE `client_mark_defaults` DISABLE KEYS */;
/*!40000 ALTER TABLE `client_mark_defaults` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_share_links`
--

DROP TABLE IF EXISTS `client_share_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_share_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `share_key` varchar(64) NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `access_key` varchar(10) DEFAULT NULL COMMENT '访问密钥',
  `task_id` int(11) DEFAULT NULL COMMENT '限制访问的任务ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `share_key` (`share_key`),
  KEY `client_id` (`client_id`),
  KEY `expires_at` (`expires_at`),
  KEY `idx_client_share_links_access_key` (`access_key`),
  KEY `idx_client_share_links_task_id` (`task_id`),
  KEY `idx_client_share_links_client_id` (`client_id`),
  KEY `idx_client_share_links_share_key` (`share_key`),
  KEY `idx_client_share_links_is_active` (`is_active`),
  KEY `idx_client_share_links_expires_at` (`expires_at`),
  CONSTRAINT `client_share_links_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_client_share_links_task_id` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='客户分享链接表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_share_links`
--

LOCK TABLES `client_share_links` WRITE;
/*!40000 ALTER TABLE `client_share_links` DISABLE KEYS */;
INSERT INTO `client_share_links` VALUES (4,1,'4dbc790d5015faeca985cb74da6f43fb',NULL,1,'2025-07-28 16:07:12','2025-07-28 16:07:12','DWYR',NULL),(5,4,'826e36b5a2145216e526830c9c754c7e',NULL,1,'2025-07-30 21:31:46','2025-07-30 21:31:46','L2SU',NULL),(6,3,'aa0df85394972c7991bc5e1bc43e15c7',NULL,1,'2025-07-31 05:24:44','2025-07-31 05:24:44','L6F7',NULL);
/*!40000 ALTER TABLE `client_share_links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `client_shares`
--

DROP TABLE IF EXISTS `client_shares`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `client_shares` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `access_token` varchar(100) NOT NULL,
  `password` varchar(20) DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `view_permission` tinyint(1) DEFAULT '1',
  `edit_permission` tinyint(1) DEFAULT '1',
  `review_permission` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `access_token` (`access_token`),
  KEY `client_id` (`client_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `client_shares_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `client_shares_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `client_shares`
--

LOCK TABLES `client_shares` WRITE;
/*!40000 ALTER TABLE `client_shares` DISABLE KEYS */;
/*!40000 ALTER TABLE `client_shares` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `clients`
--

DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `contact` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `need_review` tinyint(1) DEFAULT '1',
  `daily_content_count` int(11) DEFAULT '5',
  `display_start_time` time DEFAULT NULL,
  `interval_min` int(11) DEFAULT '30',
  `interval_max` int(11) DEFAULT '120',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  `ext_json` text,
  `default_required_topics` text COMMENT '默认必选话题(JSON格式)',
  `default_random_topics` text COMMENT '默认随机话题(JSON格式)',
  `default_at_users` text COMMENT '默认@用户(JSON格式)',
  `default_location` varchar(200) DEFAULT NULL COMMENT '默认定位信息',
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前客户的所有任务中重复使用模板',
  `auto_approved` tinyint(1) DEFAULT '0' COMMENT '是否自动审核通过（无需客户审核）',
  `review_timeout_hours` int(11) DEFAULT '24' COMMENT '审核超时小时数',
  `review_deadline_time` time DEFAULT '20:00:00' COMMENT '每日审核截止时间',
  `auto_approve_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用自动通过',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `clients`
--

LOCK TABLES `clients` WRITE;
/*!40000 ALTER TABLE `clients` DISABLE KEYS */;
INSERT INTO `clients` VALUES (1,'康师傅','','','',1,5,'08:30:00',10,30,'2025-07-14 02:17:45','2025-07-30 05:01:04',1,NULL,'[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','[\"@ee\", \"@rr\", \"@tt\", \"@yy\", \"@jj\", \"@ll\", \"@bb\"]','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]',0,0,24,'20:00:00',1),(3,'蜜雪冰城','','','',1,5,'08:30:00',10,30,'2025-07-14 02:23:55','2025-07-28 04:19:01',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1),(4,'许府牛','','','',1,5,NULL,10,30,'2025-07-15 23:13:33','2025-07-22 23:50:04',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1),(6,'娃哈哈','饿','','',1,5,'08:30:00',10,30,'2025-07-15 23:19:13','2025-07-20 23:14:58',1,'{\"remark\": \"\\u997f\"}',NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1),(8,'海底捞','','','',1,5,'08:30:00',10,30,'2025-07-16 23:24:50','2025-07-20 19:02:22',1,NULL,NULL,NULL,NULL,NULL,0,0,24,'20:00:00',1),(9,'test','','','',1,5,'08:30:00',10,30,'2025-07-29 20:06:34','2025-07-29 20:26:10',1,NULL,'[\"水电费\", \"水电费是的\", \"小彩蛋\"]','[\"111\", \"222\", \"333\", \"444\", \"555\"]','[\"@111\", \"@222\", \"@333\", \"@444\", \"@555\"]','111',0,0,24,'20:00:00',1),(21,'qqq','','','',1,5,'08:30:00',10,30,'2025-07-29 22:58:12','2025-07-30 05:00:51',1,NULL,'[\"qwe\", \"234\", \"tyrt\"]','[\"tre\", \"636\", \"fghr\"]','[\"@twerwt\", \"@45645\", \"@rty\"]','[\"wetrert\", \"45456\", \"w3453\"]',0,0,24,'20:00:00',1);
/*!40000 ALTER TABLE `clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `content_history`
--

DROP TABLE IF EXISTS `content_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `content_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `editor_id` int(11) DEFAULT NULL,
  `edit_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_client_edit` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `editor_id` (`editor_id`),
  KEY `idx_content_history_content_id` (`content_id`),
  CONSTRAINT `content_history_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `content_history_ibfk_2` FOREIGN KEY (`editor_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `content_history`
--

LOCK TABLES `content_history` WRITE;
/*!40000 ALTER TABLE `content_history` DISABLE KEYS */;
/*!40000 ALTER TABLE `content_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `content_images`
--

DROP TABLE IF EXISTS `content_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `content_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `image_path` varchar(500) NOT NULL,
  `thumbnail_path` varchar(500) DEFAULT NULL,
  `original_name` varchar(255) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `image_order` int(11) DEFAULT '1' COMMENT '图片排序',
  `upload_time` datetime DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0',
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `upload_time` (`upload_time`),
  KEY `idx_content_images_content_id` (`content_id`),
  CONSTRAINT `content_images_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COMMENT='文案图片管理表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `content_images`
--

LOCK TABLES `content_images` WRITE;
/*!40000 ALTER TABLE `content_images` DISABLE KEYS */;
INSERT INTO `content_images` VALUES (44,141,'images/202507/content_141_1753957425_af3620f1.png','thumbnails/202507/content_141_1753957425_af3620f1_thumb.png','ab6e7e8258937a49879994cd7d61d7c7.png',990363,1,'2025-07-31 18:23:45',0,NULL),(45,142,'images/202507/content_142_1753957432_ebb522a1.webp','thumbnails/202507/content_142_1753957432_ebb522a1_thumb.webp','4062d50c69a04f5389f9fd56ea010979.webp',86254,1,'2025-07-31 18:23:53',0,NULL),(46,143,'images/202507/content_143_1753957439_7da5dfa7.webp','thumbnails/202507/content_143_1753957439_7da5dfa7_thumb.webp','641.webp',98792,1,'2025-07-31 18:23:59',0,NULL),(47,144,'images/202507/content_144_1753957451_cc11fb82.webp','thumbnails/202507/content_144_1753957451_cc11fb82_thumb.webp','641_1.webp',62760,1,'2025-07-31 18:24:12',0,NULL),(48,145,'images/202507/content_145_1753957458_de49db3e.jpg','thumbnails/202507/content_145_1753957458_de49db3e_thumb.jpg','006RTEI2ly1htrvezkcm0j31712c77wh.jpg',314989,1,'2025-07-31 18:24:18',0,NULL);
/*!40000 ALTER TABLE `content_images` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `contents`
--

DROP TABLE IF EXISTS `contents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `contents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) DEFAULT NULL,
  `task_id` int(11) DEFAULT NULL,
  `batch_id` int(11) DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `topics` text,
  `location` varchar(100) DEFAULT NULL,
  `image_urls` text,
  `display_date` date DEFAULT NULL,
  `display_time` time DEFAULT NULL,
  `workflow_status` varchar(30) DEFAULT 'draft',
  `publish_status` varchar(30) DEFAULT 'unpublished',
  `client_review_status` varchar(20) DEFAULT 'pending',
  `internal_review_status` varchar(20) DEFAULT 'pending',
  `publish_priority` varchar(10) DEFAULT 'normal',
  `publish_time` datetime DEFAULT NULL,
  `status_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `reviewer_id` int(11) DEFAULT NULL,
  `review_time` datetime DEFAULT NULL,
  `ext_json` text,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `pending_image_fix` tinyint(1) DEFAULT '0' COMMENT '是否有待处理的图片问题',
  `content_completed` tinyint(1) DEFAULT '1' COMMENT '文案是否已重新编辑完成(0:未完成, 1:已完成)',
  `image_completed` tinyint(1) DEFAULT '1' COMMENT '图片是否已重新上传完成(0:未完成, 1:已完成)',
  `publish_error` text COMMENT '发布提示信息（成功或失败的详细信息）',
  `publish_retry_count` int(11) DEFAULT '0' COMMENT '发布重试次数',
  `image_editor_id` int(11) DEFAULT NULL COMMENT '图片编辑管理员ID',
  `content_editor_id` int(11) DEFAULT NULL COMMENT '文案编辑管理员ID（初审）',
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `task_id` (`task_id`),
  KEY `batch_id` (`batch_id`),
  KEY `template_id` (`template_id`),
  KEY `created_by` (`created_by`),
  KEY `reviewer_id` (`reviewer_id`),
  KEY `ix_contents_workflow_status` (`workflow_status`),
  KEY `ix_contents_publish_status` (`publish_status`),
  KEY `ix_contents_publish_priority` (`publish_priority`),
  KEY `ix_contents_is_deleted` (`is_deleted`),
  KEY `deleted_by` (`deleted_by`),
  KEY `idx_workflow_status` (`workflow_status`),
  KEY `idx_client_review_status` (`client_review_status`),
  KEY `idx_internal_review_status` (`internal_review_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_contents_completion` (`content_completed`,`image_completed`),
  KEY `idx_contents_workflow_status` (`workflow_status`),
  KEY `idx_contents_client_id` (`client_id`),
  KEY `idx_contents_task_id` (`task_id`),
  KEY `idx_contents_batch_id` (`batch_id`),
  KEY `idx_contents_created_at` (`created_at`),
  KEY `idx_contents_display_date` (`display_date`),
  KEY `idx_contents_publish_time` (`publish_time`),
  KEY `idx_contents_client_review_status` (`client_review_status`),
  KEY `idx_contents_workflow_client` (`workflow_status`,`client_id`),
  KEY `idx_contents_workflow_created` (`workflow_status`,`created_at`),
  KEY `idx_contents_client_created` (`client_id`,`created_at`),
  KEY `idx_contents_image_editor_id` (`image_editor_id`),
  KEY `idx_contents_content_editor_id` (`content_editor_id`),
  CONSTRAINT `contents_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `contents_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  CONSTRAINT `contents_ibfk_3` FOREIGN KEY (`batch_id`) REFERENCES `batches` (`id`),
  CONSTRAINT `contents_ibfk_4` FOREIGN KEY (`template_id`) REFERENCES `templates` (`id`),
  CONSTRAINT `contents_ibfk_5` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_6` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`),
  CONSTRAINT `contents_ibfk_7` FOREIGN KEY (`deleted_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_contents_content_editor` FOREIGN KEY (`content_editor_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_contents_image_editor` FOREIGN KEY (`image_editor_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=146 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contents`
--

LOCK TABLES `contents` WRITE;
/*!40000 ALTER TABLE `contents` DISABLE KEYS */;
INSERT INTO `contents` VALUES (141,1,21,39,101,'减肥也能吃！✅ 22轻食系列33太✅ 了','减肥也能吃！✅ 22轻食系列33太✅ 了','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','yy',NULL,'2025-07-31','08:30:00','pending_client_review','unpublished','pending','approved','normal',NULL,'2025-07-31 18:48:58','2025-07-31 18:09:33','2025-07-31 18:48:58',1,1,'2025-07-31 18:48:58','{\"at_users\": [\"jj\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(142,1,21,39,102,'节日限定✅ 2233错过等一年！','✅ 44✅ 去\r\n圣诞季必吃的限定款33！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"tt\", \"bb\", \"ll\"]','ll',NULL,'2025-07-31','08:41:00','pending_client_review','unpublished','pending','approved','normal',NULL,'2025-07-31 18:37:53','2025-07-31 18:09:33','2025-07-31 18:37:53',1,1,'2025-07-31 18:37:53','{\"at_users\": [\"@bb\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(143,1,21,39,99,'✅ 打工人午餐救星！223315分钟上菜⚡','✅ 44✅ \r\n工作日中午的快乐源泉～33量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"jj\", \"ll\", \"ee\"]','rr',NULL,'2025-07-31','09:12:00','pending_client_review','unpublished','pending','approved','normal',NULL,'2025-07-31 18:40:45','2025-07-31 18:09:33','2025-07-31 18:40:45',1,1,'2025-07-31 18:40:45','{\"at_users\": [\"@bb\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(144,1,21,39,100,'家庭聚餐就选这✅ 2233老少皆宜✅','家庭聚餐就选这✅ 2233老少皆宜✅','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\"]','bb',NULL,'2025-07-31','09:04:00','pending_client_review','unpublished','pending','approved','normal',NULL,'2025-07-31 19:06:27','2025-07-31 18:09:33','2025-07-31 19:06:27',1,1,'2025-07-31 19:06:27','{\"at_users\": [\"rr\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2),(145,1,21,39,94,'✅ 挖到宝了✨22的33也太香了吧！','✅ 44✅ \r\n路过被香味吸引进店，33直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店','[\"ee\", \"rr\", \"tt\", \"yy\", \"jj\", \"ll\", \"bb\", \"ll\", \"jj\", \"rr\"]','ll',NULL,'2025-07-31','09:28:00','final_review','unpublished','pending','pending','normal',NULL,'2025-07-31 19:07:55','2025-07-31 18:09:33','2025-07-31 19:07:55',1,2,'2025-07-31 19:07:55','{\"at_users\": [\"@ll\"]}',0,NULL,NULL,0,1,1,NULL,0,2,2);
/*!40000 ALTER TABLE `contents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_schedules`
--

DROP TABLE IF EXISTS `display_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_schedules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `display_date` date NOT NULL,
  `display_time` time NOT NULL,
  `is_fixed_time` tinyint(1) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `display_order` int(11) DEFAULT NULL,
  `actual_display_time` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `client_id` (`client_id`),
  KEY `content_id` (`content_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_schedules`
--

LOCK TABLES `display_schedules` WRITE;
/*!40000 ALTER TABLE `display_schedules` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_schedules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `display_settings`
--

DROP TABLE IF EXISTS `display_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `display_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `order_type` varchar(20) DEFAULT NULL,
  `custom_order` text,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_id` (`client_id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `display_settings`
--

LOCK TABLES `display_settings` WRITE;
/*!40000 ALTER TABLE `display_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `display_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `menu_items`
--

DROP TABLE IF EXISTS `menu_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `menu_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `permission` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `order` int(11) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_menu_items_parent_id` (`parent_id`),
  KEY `idx_menu_items_order` (`order`),
  KEY `idx_menu_items_is_active` (`is_active`),
  CONSTRAINT `fk_menu_items_parent` FOREIGN KEY (`parent_id`) REFERENCES `menu_items` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `menu_items`
--

LOCK TABLES `menu_items` WRITE;
/*!40000 ALTER TABLE `menu_items` DISABLE KEYS */;
INSERT INTO `menu_items` VALUES (1,'控制台','/simple/dashboard','bi bi-speedometer2','dashboard.view',NULL,1,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(2,'模板管理','/simple/templates','bi bi-layer-group','template.manage',NULL,2,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(3,'客户管理','/simple/clients','bi bi-people','client.manage',NULL,3,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(4,'内容生成','/simple/content','bi bi-pencil-square','content.generate',NULL,4,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(5,'初审文案','/simple/review-content','bi bi-clipboard-check','content.review',NULL,5,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(6,'图片上传','/simple/image-upload','bi bi-image','image.upload',NULL,6,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(7,'最终审核','/simple/final-review','bi bi-check2-square','content.final_review',NULL,7,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(8,'客户审核','/simple/client-review','bi bi-person-check','client.review',NULL,8,1,'2025-07-27 13:31:20','2025-07-27 13:31:20'),(9,'发布管理','/simple/publish-manage','bi bi-send','publish.manage',NULL,9,1,'2025-07-27 13:31:21','2025-07-27 13:31:21'),(10,'发布状态','/simple/publish-status-manage','bi bi-list-check','publish.status',NULL,10,1,'2025-07-27 13:31:21','2025-07-27 13:31:21'),(11,'用户管理','/simple/users','bi bi-person-gear','user.manage',NULL,11,1,'2025-07-27 13:31:21','2025-07-27 13:31:21'),(12,'系统设置','/simple/system','bi bi-gear','system.settings',NULL,12,1,'2025-07-27 13:31:21','2025-07-27 13:31:21');
/*!40000 ALTER TABLE `menu_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `type` varchar(30) NOT NULL,
  `related_content_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `recipient_id` int(11) NOT NULL,
  `priority` varchar(10) DEFAULT 'normal',
  PRIMARY KEY (`id`),
  KEY `related_content_id` (`related_content_id`),
  KEY `recipient_id` (`recipient_id`),
  KEY `ix_notifications_created_at` (`created_at`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`related_content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'user.view','查看用户'),(2,'user.manage','管理用户'),(3,'client.view','查看客户'),(4,'client.manage','管理客户'),(5,'template.view','查看模板'),(6,'template.manage','管理模板'),(7,'content.view','查看文案'),(8,'content.manage','管理文案'),(9,'review.first','初审管理'),(10,'image.view','查看图片'),(11,'image.manage','管理图片'),(12,'review.final','终审管理'),(13,'publish.view','查看发布'),(14,'publish.manage','管理发布'),(15,'system.settings','系统设置'),(16,'notification.view','查看通知'),(17,'notification.manage','管理通知'),(48,'dashboard_access','控制面板'),(49,'user_manage','用户管理'),(50,'client_manage','客户管理'),(51,'template_manage','模板管理'),(52,'content_manage','文案管理'),(53,'content_generate','生成文案'),(54,'topic_manage','话题管理'),(55,'task_manage','任务管理'),(56,'publish_manage','发布管理'),(57,'supplement_manage','文案补充'),(58,'display_manage','文案展示'),(59,'notification_manage','通知中心'),(60,'stats_view','数据统计'),(61,'export_manage','导入导出'),(62,'system_settings','系统设置'),(63,'image_manage','图片管理权限'),(64,'client_review_manage','客户审核管理权限'),(65,'publish_manage_new','发布管理权限（新版）'),(66,'dashboard.view','控制台查看'),(67,'content.generate','内容生成'),(68,'content.review','内容审核'),(69,'image.upload','图片上传'),(70,'content.final_review','最终审核'),(71,'client.review','客户审核'),(72,'publish.status','发布状态');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions_backup`
--

DROP TABLE IF EXISTS `permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions_backup` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions_backup`
--

LOCK TABLES `permissions_backup` WRITE;
/*!40000 ALTER TABLE `permissions_backup` DISABLE KEYS */;
INSERT INTO `permissions_backup` VALUES (1,'查看用户列表','查看系统用户列表'),(2,'新增用户','创建新的系统用户'),(3,'编辑用户','编辑系统用户信息'),(4,'删除用户','删除系统用户'),(5,'分配用户角色','为用户分配系统角色'),(6,'查看客户列表','查看所有客户'),(7,'新增客户','添加新客户'),(8,'编辑客户','编辑客户信息'),(9,'删除客户','删除客户'),(10,'创建客户分享链接','为客户创建分享链接'),(11,'管理客户审核权限','管理客户的审核权限'),(12,'查看模板列表','查看所有模板'),(13,'新增模板','创建新的模板'),(14,'编辑模板','编辑现有模板'),(15,'删除模板','删除模板'),(16,'管理模板分类','管理模板分类'),(17,'查看文案列表','查看所有文案'),(18,'新建文案任务','创建新的文案生成任务'),(19,'批量生成文案','批量生成文案内容'),(20,'编辑文案','编辑文案内容'),(21,'预览文案','预览文案效果'),(22,'删除文案','删除文案'),(23,'查看待初审文案','查看待初审的文案列表'),(24,'初审通过','将文案标记为初审通过'),(25,'初审编辑','在初审阶段编辑文案'),(26,'初审驳回','驳回初审文案'),(27,'批量初审操作','批量处理初审文案'),(28,'查看待上传图片文案','查看需要上传图片的文案'),(29,'上传图片','为文案上传图片'),(30,'编辑图片','编辑文案图片'),(31,'删除图片','删除文案图片'),(32,'提交图片审核','提交图片进入下一审核流程'),(33,'查看待终审文案','查看待终审的文案'),(34,'终审通过','将文案标记为终审通过'),(35,'终审编辑','在终审阶段编辑文案'),(36,'终审驳回','驳回终审文案'),(37,'批量终审操作','批量处理终审文案'),(38,'查看待发布文案','查看待发布的文案'),(39,'设置发布优先级','设置文案发布优先级'),(40,'查看发布状态','查看文案发布状态'),(41,'手动更新发布状态','手动更新文案发布状态'),(42,'发布失败处理','处理发布失败的文案'),(43,'查看系统设置','查看系统配置选项'),(44,'修改系统配置','修改系统配置选项'),(45,'管理审核流程','管理文案审核流程'),(46,'管理快捷理由','管理拒绝快捷理由'),(47,'查看系统日志','查看系统操作日志'),(48,'user.view','查看用户'),(49,'user.create','创建用户'),(50,'user.edit','编辑用户'),(51,'user.delete','删除用户'),(52,'user.manage','管理用户'),(53,'client.view','查看客户'),(54,'client.create','创建客户'),(55,'client.edit','编辑客户'),(56,'client.delete','删除客户'),(57,'client.manage','管理客户'),(58,'template.view','查看模板'),(59,'template.create','创建模板'),(60,'template.edit','编辑模板'),(61,'template.delete','删除模板'),(62,'template.manage','管理模板'),(63,'content.view','查看文案'),(64,'content.create','创建文案'),(65,'content.edit','编辑文案'),(66,'content.delete','删除文案'),(67,'content.manage','管理文案'),(68,'review.first.view','查看初审列表'),(69,'review.first.approve','初审通过'),(70,'review.first.reject','初审驳回'),(71,'review.first.manage','管理初审(包含所有初审相关权限)'),(72,'image.view','查看图片'),(73,'image.upload','上传图片'),(74,'image.edit','编辑图片'),(75,'image.delete','删除图片'),(76,'image.manage','管理图片'),(77,'review.final.view','查看终审列表'),(78,'review.final.approve','终审通过'),(79,'review.final.reject','终审驳回'),(80,'review.final.manage','管理终审(包含所有终审相关权限)'),(81,'publish.view','查看发布'),(82,'publish.approve','发布文案'),(83,'publish.manage','管理发布'),(84,'system.view','查看系统设置'),(85,'system.edit','编辑系统设置'),(86,'system.manage','管理系统(包含所有系统相关权限)'),(87,'review.first','初审管理'),(88,'review.final','终审管理'),(89,'system.settings','系统设置'),(90,'notification.view','查看通知'),(91,'notification.manage','管理通知');
/*!40000 ALTER TABLE `permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_records`
--

DROP TABLE IF EXISTS `publish_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `status` varchar(20) DEFAULT NULL,
  `platform` varchar(50) DEFAULT NULL,
  `account` varchar(100) DEFAULT NULL,
  `publish_url` varchar(255) DEFAULT NULL,
  `publish_time` datetime DEFAULT NULL,
  `error_message` text,
  `ext_info` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  CONSTRAINT `publish_records_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_records`
--

LOCK TABLES `publish_records` WRITE;
/*!40000 ALTER TABLE `publish_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `publish_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `publish_timeouts`
--

DROP TABLE IF EXISTS `publish_timeouts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `publish_timeouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `timeout_minutes` int(11) DEFAULT NULL,
  `action` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `updated_by` (`updated_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `publish_timeouts`
--

LOCK TABLES `publish_timeouts` WRITE;
/*!40000 ALTER TABLE `publish_timeouts` DISABLE KEYS */;
/*!40000 ALTER TABLE `publish_timeouts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quick_reasons`
--

DROP TABLE IF EXISTS `quick_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `quick_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` varchar(200) NOT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quick_reasons`
--

LOCK TABLES `quick_reasons` WRITE;
/*!40000 ALTER TABLE `quick_reasons` DISABLE KEYS */;
/*!40000 ALTER TABLE `quick_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rejection_reasons`
--

DROP TABLE IF EXISTS `rejection_reasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rejection_reasons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content_id` int(11) NOT NULL,
  `reason` text NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `is_client` tinyint(1) DEFAULT '0',
  `rejection_type` varchar(20) DEFAULT 'content' COMMENT '驳回类型：content=文案问题, image=图片问题, both=两者都有问题',
  PRIMARY KEY (`id`),
  KEY `content_id` (`content_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_rejection_reasons_type` (`rejection_type`),
  KEY `idx_rejection_reasons_content_type` (`content_id`,`rejection_type`),
  CONSTRAINT `rejection_reasons_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `contents` (`id`),
  CONSTRAINT `rejection_reasons_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rejection_reasons`
--

LOCK TABLES `rejection_reasons` WRITE;
/*!40000 ALTER TABLE `rejection_reasons` DISABLE KEYS */;
INSERT INTO `rejection_reasons` VALUES (27,145,'文案长度不合适，需要调整','2025-07-31 18:25:42',1,0,'content'),(28,144,'文案长度不合适，需要调整','2025-07-31 18:25:42',1,0,'content'),(29,142,'文案长度不合适，需要调整','2025-07-31 18:25:42',1,0,'content'),(30,143,'文案长度不合适，需要调整','2025-07-31 18:25:42',1,0,'content'),(31,141,'文案长度不合适，需要调整','2025-07-31 18:25:42',1,0,'content'),(32,142,'文案风格与品牌调性不符','2025-07-31 18:32:49',1,0,'content'),(33,143,'内容逻辑不清晰，需要重新组织','2025-07-31 18:35:05',NULL,1,'content');
/*!40000 ALTER TABLE `rejection_reasons` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (32,3,7),(33,3,8),(35,7,7),(36,7,10),(37,7,11),(38,4,3),(39,4,4),(44,8,7),(45,8,12),(47,9,5),(48,9,6),(50,5,13),(51,5,14),(52,5,16),(53,5,17),(75,2,66),(76,2,70),(77,2,14),(78,2,69),(79,2,6),(80,2,15),(81,2,68),(82,2,72),(83,2,4),(84,2,2),(85,2,67),(86,2,71),(87,10,15),(88,10,66),(89,10,71),(90,10,2),(91,10,68),(92,10,70),(93,10,72),(94,10,14),(95,10,69),(96,10,4),(97,10,67),(98,10,6),(99,11,66),(100,11,67),(101,11,69),(102,11,68),(103,12,66),(104,12,71),(105,12,68),(106,12,70),(107,6,66);
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions_backup`
--

DROP TABLE IF EXISTS `role_permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_permissions_backup` (
  `id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions_backup`
--

LOCK TABLES `role_permissions_backup` WRITE;
/*!40000 ALTER TABLE `role_permissions_backup` DISABLE KEYS */;
INSERT INTO `role_permissions_backup` VALUES (186,2,1),(187,2,2),(188,2,3),(189,2,4),(190,2,5),(191,2,6),(192,2,7),(193,2,8),(194,2,9),(195,2,10),(196,2,11),(197,2,12),(198,2,13),(199,2,14),(200,2,15),(201,2,16),(202,2,17),(203,2,18),(204,2,19),(205,2,20),(206,2,21),(207,2,22),(208,2,23),(209,2,24),(210,2,25),(211,2,26),(212,2,27),(213,2,28),(214,2,29),(215,2,30),(216,2,31),(217,2,32),(218,2,33),(219,2,34),(220,2,35),(221,2,36),(222,2,37),(223,2,38),(224,2,39),(225,2,40),(226,2,41),(227,2,42),(228,2,43),(229,2,44),(230,2,45),(231,2,46),(232,2,47),(233,2,48),(234,2,49),(235,2,50),(236,2,51),(237,2,52),(238,2,53),(239,2,54),(240,2,55),(241,2,56),(242,2,57),(243,2,58),(244,2,59),(245,2,60),(246,2,61),(247,2,62),(248,2,63),(249,2,64),(250,2,65),(251,2,66),(252,2,67),(253,2,68),(254,2,69),(255,2,70),(256,2,71),(257,2,72),(258,2,73),(259,2,74),(260,2,75),(261,2,76),(262,2,77),(263,2,78),(264,2,79),(265,2,80),(266,2,81),(267,2,82),(268,2,83),(269,2,84),(270,2,85),(271,2,86),(272,2,87),(273,2,88),(274,2,89),(275,2,90),(276,2,91),(277,3,63),(278,3,67),(279,7,63),(280,7,72),(281,7,76),(282,4,53),(283,4,57),(284,6,63),(285,6,58),(286,8,63),(287,8,88),(288,9,58),(289,9,62),(290,5,81),(291,5,83),(292,5,90),(293,5,91);
/*!40000 ALTER TABLE `role_permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (2,'超级管理员','系统超级管理员，拥有所有权限','2025-07-13 02:49:12'),(3,'内容编辑','负责编辑文案内容','2025-07-13 10:39:29'),(4,'客户经理','负责管理客户','2025-07-13 10:39:29'),(5,'运营专员','负责内容发布和运营','2025-07-13 10:39:29'),(6,'普通用户','普通系统用户','2025-07-13 10:39:29'),(7,'图文编辑','负责管理图片和文案','2025-07-15 15:55:41'),(8,'最终审核员','负责内容终审','2025-07-15 15:55:41'),(9,'模板管理员','负责管理模板','2025-07-15 15:59:04'),(10,'管理员','拥有大部分管理权限','2025-07-27 13:31:21'),(11,'编辑','拥有内容编辑权限','2025-07-27 13:31:21'),(12,'审核员','拥有内容审核权限','2025-07-27 13:31:21');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_settings`
--

DROP TABLE IF EXISTS `system_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) NOT NULL,
  `value` text,
  `description` varchar(200) DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_settings`
--

LOCK TABLES `system_settings` WRITE;
/*!40000 ALTER TABLE `system_settings` DISABLE KEYS */;
INSERT INTO `system_settings` VALUES (3,'auto_publish_enabled','1','是否启用自动发布','2025-07-29 23:22:47',1),(11,'ENABLE_FIRST_REVIEW','1','是否启用初审功能','2025-07-28 20:58:32',1),(12,'ENABLE_FINAL_REVIEW','1','是否启用最终审核功能','2025-07-28 21:37:11',1),(13,'IMAGE_UPLOAD_MAX_SIZE','10485760','图片上传最大大小（字节）','2025-07-28 17:59:16',1),(14,'IMAGE_UPLOAD_ALLOWED_TYPES','jpg,jpeg,png,gif,webp','允许上传的图片类型','2025-07-28 17:59:16',1),(16,'MAX_IMAGES_PER_CONTENT','9','每篇文案最大图片数量','2025-07-28 17:59:16',1),(17,'API_KEY','fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW','API访问密钥','2025-07-28 17:59:16',1),(18,'PUBLISH_TIMEOUT','120','发布超时时间（秒）- 测试设置为2分钟','2025-07-28 17:59:16',1),(19,'PUBLISH_TIMEOUT_ACTION','keep_timeout','超时处理策略：保持超时状态','2025-07-28 17:59:23',1),(20,'PUBLISH_MAX_RETRY_COUNT','3','发布最大重试次数','2025-07-28 17:59:16',1);
/*!40000 ALTER TABLE `system_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` varchar(20) DEFAULT 'processing',
  `target_count` int(11) DEFAULT '0',
  `actual_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  `allow_template_duplicate` tinyint(1) DEFAULT '0' COMMENT '是否允许在当前任务中重复使用模板',
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `tasks_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
  CONSTRAINT `tasks_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tasks`
--

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;
INSERT INTO `tasks` VALUES (21,1,'2025年07月31日任务',NULL,'processing',0,0,'2025-07-31 18:09:33','2025-07-31 18:09:33',1,0);
/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_categories`
--

DROP TABLE IF EXISTS `template_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `template_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `template_categories` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_categories`
--

LOCK TABLES `template_categories` WRITE;
/*!40000 ALTER TABLE `template_categories` DISABLE KEYS */;
INSERT INTO `template_categories` VALUES (1,'美妆护肤',NULL,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'时尚穿搭',NULL,2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'美食探店',NULL,3,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'旅游攻略',NULL,4,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'生活分享',NULL,5,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'护肤心得',1,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'彩妆教程',1,2,'2025-07-13 10:39:29','2025-07-17 02:38:25'),(8,'穿搭搭配',2,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'潮流趋势',2,2,'2025-07-13 10:39:29','2025-07-19 01:25:22'),(10,'餐厅推荐',3,1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(11,'美食制作',3,2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `template_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `template_marks`
--

DROP TABLE IF EXISTS `template_marks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `template_marks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(200) DEFAULT NULL,
  `type` varchar(20) DEFAULT 'text',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `template_marks`
--

LOCK TABLES `template_marks` WRITE;
/*!40000 ALTER TABLE `template_marks` DISABLE KEYS */;
INSERT INTO `template_marks` VALUES (1,'品牌名称','','text','2025-07-13 17:32:29'),(2,'店铺地址','','text','2025-07-13 17:33:24'),(4,'标记2','','text','2025-07-13 17:34:01'),(5,'标记3','','text','2025-07-13 17:34:05'),(7,'标记5','','text','2025-07-13 17:34:13'),(8,'商品名称','','text','2025-07-13 18:24:44'),(9,'标记6','','text','2025-07-17 02:45:13'),(11,'标记8','11','text','2025-07-17 02:59:22');
/*!40000 ALTER TABLE `template_marks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `templates`
--

DROP TABLE IF EXISTS `templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `creator_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` tinyint(1) DEFAULT '1',
  `marks` json DEFAULT NULL COMMENT '模板中的标记列表，JSON格式存储',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  KEY `creator_id` (`creator_id`),
  CONSTRAINT `templates_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `template_categories` (`id`),
  CONSTRAINT `templates_ibfk_2` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `templates`
--

LOCK TABLES `templates` WRITE;
/*!40000 ALTER TABLE `templates` DISABLE KEYS */;
INSERT INTO `templates` VALUES (93,11,'{品牌名称}必点单品！✅ {商品名称}一口就爱上✅ ','✅ {店铺地址}✅ \r\n今天终于吃到{品牌名称}的招牌{商品名称}，口感绝了！外脆里嫩，酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常',1,'2025-07-20 17:54:35','2025-07-27 01:20:44',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(94,11,'✅ 挖到宝了✨{品牌名称}的{商品名称}也太香了吧！','✅ {店铺地址}✅ \r\n路过被香味吸引进店，{商品名称}直接封神！分量超足，人均30+吃到撑～姐妹们快冲 #高性价比美食 #周末探店',1,'2025-07-20 17:54:53','2025-07-27 01:20:32',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(95,1,'本地人才知道的隐藏美味！✅ {品牌名称}{商品名称}✅ ','✅ {店铺地址}✅ \r\n同事强烈安利的{商品名称}，果然没让我失望！独家秘制配方，吃完还想打包十份带走！ #地方美食 #必吃清单',1,'2025-07-20 17:55:05','2025-07-27 01:21:10',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(96,1,'早餐新选择！✅ {品牌名称}{商品名称}开启元气一天☀️','✅ {店铺地址}\r\n早起就是为了这口{商品名称}！现点现做，酥脆爆浆，搭配咖啡绝绝子～打工人早餐首选 #早餐推荐 #美食日记',1,'2025-07-20 17:55:18','2025-07-27 01:21:18',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(97,11,'深夜放毒时间到！✅ {品牌名称}{商品名称}太罪恶了✅ ','✅ {店铺地址}\r\n半夜饿到不行发现{品牌名称}还营业！✅ {商品名称}热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食堂',1,'2025-07-20 17:55:36','2025-07-27 01:21:32',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(98,11,'✅ 闺蜜下午茶首选?✅ {品牌名称}{商品名称}颜值味道双在线！','✅  {店铺地址}✅ \r\n和姐妹约会的秘密基地！{商品名称}不仅拍照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会',1,'2025-07-20 17:55:56','2025-07-27 01:20:53',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(99,11,'✅ 打工人午餐救星！{品牌名称}{商品名称}15分钟上菜⚡','✅ {店铺地址}✅ \r\n工作日中午的快乐源泉～{商品名称}量大实惠，上菜速度感人！已经连续吃一周了 #工作餐推荐 #快餐美食',1,'2025-07-20 17:56:12','2025-07-27 01:22:08',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(100,11,'家庭聚餐就选这✅ {品牌名称}{商品名称}老少皆宜✅ ','✅ {店铺地址}\r\n带全家来吃{品牌名称}，✅ {商品名称}获得一致好评！爸妈都说下次还要来～ #家庭餐厅 #聚餐推荐',1,'2025-07-20 17:56:32','2025-07-27 01:21:45',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(101,11,'减肥也能吃！✅ {品牌名称}轻食系列{商品名称}太✅ 了','✅ {店铺地址}\r\n健身教练推荐的{商品名称}，低卡高蛋白，吃完毫无负担！已经加入我的每周必点清单 #健康餐 #减脂美食',1,'2025-07-20 17:56:47','2025-07-27 01:21:56',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]'),(102,11,'节日限定✅ {品牌名称}{商品名称}错过等一年！','✅ {店铺地址}✅ 去\r\n圣诞季必吃的限定款{商品名称}！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食',1,'2025-07-20 17:57:00','2025-07-28 19:44:57',1,'[\"品牌名称\", \"商品名称\", \"店铺地址\"]');
/*!40000 ALTER TABLE `templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic_relations`
--

DROP TABLE IF EXISTS `topic_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topic_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `topic_id` int(11) NOT NULL,
  `related_topic_id` int(11) NOT NULL,
  `weight` int(11) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `topic_id` (`topic_id`),
  KEY `related_topic_id` (`related_topic_id`),
  CONSTRAINT `topic_relations_ibfk_1` FOREIGN KEY (`topic_id`) REFERENCES `topics` (`id`),
  CONSTRAINT `topic_relations_ibfk_2` FOREIGN KEY (`related_topic_id`) REFERENCES `topics` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic_relations`
--

LOCK TABLES `topic_relations` WRITE;
/*!40000 ALTER TABLE `topic_relations` DISABLE KEYS */;
/*!40000 ALTER TABLE `topic_relations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topics`
--

DROP TABLE IF EXISTS `topics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `topics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) DEFAULT 'random',
  `priority` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topics`
--

LOCK TABLES `topics` WRITE;
/*!40000 ALTER TABLE `topics` DISABLE KEYS */;
INSERT INTO `topics` VALUES (1,'#美妆分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(2,'#护肤心得','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(3,'#穿搭搭配','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(4,'#美食探店','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(5,'#旅游攻略','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(6,'#生活分享','random',1,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(7,'#好物推荐','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(8,'#购物分享','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(9,'#职场穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29'),(10,'#约会穿搭','random',2,'2025-07-13 10:39:29','2025-07-13 10:39:29');
/*!40000 ALTER TABLE `topics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_menu_permissions`
--

DROP TABLE IF EXISTS `user_menu_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_menu_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_menu` (`user_id`,`menu_id`),
  KEY `menu_id` (`menu_id`),
  CONSTRAINT `user_menu_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_menu_permissions_ibfk_2` FOREIGN KEY (`menu_id`) REFERENCES `menu_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_menu_permissions`
--

LOCK TABLES `user_menu_permissions` WRITE;
/*!40000 ALTER TABLE `user_menu_permissions` DISABLE KEYS */;
INSERT INTO `user_menu_permissions` VALUES (9,1,1),(12,1,2),(14,1,3),(4,1,4),(6,1,5),(8,1,6),(11,1,7),(13,1,8),(3,1,9),(5,1,10),(7,1,11),(10,1,12),(19,2,1),(15,2,5),(55,2,6),(18,3,1),(46,3,4),(17,3,6),(20,4,1),(47,4,4),(21,4,7),(48,6,4),(23,6,9),(22,6,10),(2,7,1),(42,7,2),(34,7,3),(35,7,4),(36,7,5),(37,7,6),(38,7,7),(39,7,8),(40,7,9),(41,7,10),(43,7,11),(44,7,12),(52,13,1),(51,13,5),(53,14,1),(54,14,6);
/*!40000 ALTER TABLE `user_menu_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_permissions`
--

DROP TABLE IF EXISTS `user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_permissions`
--

LOCK TABLES `user_permissions` WRITE;
/*!40000 ALTER TABLE `user_permissions` DISABLE KEYS */;
INSERT INTO `user_permissions` VALUES (1,1,48),(2,1,49),(3,1,50),(4,1,51),(5,1,52),(6,1,53),(7,1,54),(8,1,55),(9,1,56),(10,1,57),(11,1,58),(12,1,59),(13,1,60),(14,1,61),(15,1,62),(16,7,51),(17,7,51),(18,7,53),(19,7,53),(20,7,14),(21,7,48),(22,7,50),(23,7,52),(24,7,63),(25,7,12),(26,7,62),(27,2,68),(28,4,68),(29,3,69);
/*!40000 ALTER TABLE `user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_permissions_backup`
--

DROP TABLE IF EXISTS `user_permissions_backup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_permissions_backup` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_permissions_backup`
--

LOCK TABLES `user_permissions_backup` WRITE;
/*!40000 ALTER TABLE `user_permissions_backup` DISABLE KEYS */;
INSERT INTO `user_permissions_backup` VALUES (12,7,46),(13,7,29),(14,7,42),(15,7,54),(16,7,47),(17,7,58),(18,7,27),(19,7,53),(20,7,57),(21,7,30),(22,7,48);
/*!40000 ALTER TABLE `user_permissions_backup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_role`
--

DROP TABLE IF EXISTS `user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `user_id` (`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_role`
--

LOCK TABLES `user_role` WRITE;
/*!40000 ALTER TABLE `user_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES (1,1,2),(2,1,2),(3,2,3),(4,3,7),(5,4,8),(6,5,6),(7,6,5);
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `last_login` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_users_username` (`username`),
  UNIQUE KEY `ix_users_email` (`email`),
  KEY `ix_users_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','管理员','','2025-07-13 02:49:12','2025-07-31 01:53:40',1),(2,'reviewer','scrypt:32768:8:1$NCpOQWmmiMGT3Pd2$4a8a6467bccc9ef82688931534a570431bcc68ccde28f1c4f0cfd5a1af0599951d675335d8e666a47b222be0c262254e8ab6098711aca80cacf463972237da92','<EMAIL>','审核张三','','2025-07-15 15:55:41','2025-07-31 03:31:52',1),(3,'editor','scrypt:32768:8:1$VpmDj9Os7VVM2S2D$048767c53a123a50276547ada3082b6a1a433651e304a118b97072cf1d5984aeb31bd42cb98b7c581e6b90fe3ad0495a23cdbedb8ae33e55d3cedc8a5275d89e','<EMAIL>','图文编辑','','2025-07-15 15:55:41','2025-07-30 17:56:48',1),(4,'final_reviewer','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','最终审核员','','2025-07-15 15:55:41',NULL,1),(5,'client','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','客户用户',NULL,'2025-07-15 15:55:41',NULL,1),(6,'publisher','scrypt:32768:8:1$jC2NoFyNXqBCgj0W$30873b394b1d325e5c8472c1b79a89b7a5635c99c13893ef02871425f996ce3a04c316eff7fb715eeb7742f225e91bc2178793f675d4ca1f1e4843e682b1c663','<EMAIL>','发布管理员','','2025-07-15 15:55:41',NULL,1),(7,'template_manager','scrypt:32768:8:1$EiLHEhiwakCKqJg1$8de1a3451c6fdbcaa1993d83b32d4cf0cb084ce0f727c3c6aeb3ea14204b90bf1aba6f370d069347b1f84c405f6a531c460262f23a32fd2e6a9aa2267fc773e1','<EMAIL>','模板管理员','','2025-07-15 15:59:04','2025-07-28 18:38:45',1),(13,'reviewer2','scrypt:32768:8:1$j4qYfmhKQl94ueJO$321c7d2e97a60d377b0b673a0069632824c300b0a9697896e895ca079aa0b799bc7c0ff7e4f58a19522b8858eafe0e848b41e78297605dd5be3746fd3d95ec7f',NULL,'审核李四','','2025-07-30 16:11:03','2025-07-30 16:48:04',1),(14,'editor2','scrypt:32768:8:1$8vbFe87LTCOjX4a5$38c2c6d0e402e2c9d1f3a351ae48148fdacd8b8609508ea38a70eea9213b0deaa388106e39ced697039eb367fa83c625754208be57335e8b6530c4b2573f04e9',NULL,'图片赵六','','2025-07-30 17:55:28','2025-07-30 18:02:02',1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-31 19:38:16
