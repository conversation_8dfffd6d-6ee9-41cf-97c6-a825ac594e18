#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
URL辅助工具
"""

from flask import request, current_app
from urllib.parse import urlparse


def get_external_base_url():
    """
    获取外部访问的基础URL
    
    优先级：
    1. 代理头信息（X-Forwarded-Proto, X-Forwarded-Host）
    2. 配置的外部域名（EXTERNAL_DOMAIN）
    3. Flask检测的域名（request.scheme, request.host）
    
    Returns:
        str: 外部访问的基础URL，如 https://xhs2.ke999.cn
    """
    try:
        # 1. 优先使用代理头信息
        forwarded_proto = request.headers.get('X-Forwarded-Proto')
        forwarded_host = request.headers.get('X-Forwarded-Host')
        
        if forwarded_proto and forwarded_host:
            return f"{forwarded_proto}://{forwarded_host}"
        
        # 2. 使用配置的外部域名（仅在生产环境）
        external_domain = current_app.config.get('EXTERNAL_DOMAIN')
        if external_domain:
            # 如果配置的域名已经包含协议，直接使用
            if external_domain.startswith(('http://', 'https://')):
                parsed = urlparse(external_domain)
                return f"{parsed.scheme}://{parsed.netloc}"
            else:
                # 如果没有协议，默认使用https
                return f"https://{external_domain}"

        # 3. 回退到Flask检测的域名
        flask_url = f"{request.scheme}://{request.host}"

        # 如果是本地开发环境，直接使用Flask检测的域名
        if request.host.startswith(('127.0.0.1', 'localhost')):
            return flask_url

        # 如果不是本地环境但没有配置外部域名，使用默认的生产域名
        return "https://xhs2.ke999.cn"
        
    except Exception as e:
        current_app.logger.warning(f"获取外部域名失败: {e}")
        # 最后的回退方案
        return "https://xhs2.ke999.cn"


def build_share_url(share_key, access_key=None, task_id=None):
    """
    构建分享链接URL
    
    Args:
        share_key: 分享密钥
        access_key: 访问密钥（可选）
        task_id: 任务ID（可选）
    
    Returns:
        str: 完整的分享链接URL
    """
    try:
        base_url = get_external_base_url()
        share_url = f"{base_url}/client-review/{share_key}"
        
        # 添加参数
        params = []
        if access_key:
            params.append(f"key={access_key}")
        if task_id:
            params.append(f"task={task_id}")
        
        if params:
            share_url += "?" + "&".join(params)
        
        return share_url
        
    except Exception as e:
        current_app.logger.error(f"构建分享链接失败: {e}")
        # 回退方案
        return f"/client-review/{share_key}"


def log_request_info():
    """
    记录请求信息，用于调试
    """
    try:
        current_app.logger.info(f"请求信息调试:")
        current_app.logger.info(f"  - request.scheme: {request.scheme}")
        current_app.logger.info(f"  - request.host: {request.host}")
        current_app.logger.info(f"  - request.url: {request.url}")
        current_app.logger.info(f"  - X-Forwarded-Proto: {request.headers.get('X-Forwarded-Proto')}")
        current_app.logger.info(f"  - X-Forwarded-Host: {request.headers.get('X-Forwarded-Host')}")
        current_app.logger.info(f"  - Host: {request.headers.get('Host')}")
        current_app.logger.info(f"  - 配置的外部域名: {current_app.config.get('EXTERNAL_DOMAIN')}")
        current_app.logger.info(f"  - 最终基础URL: {get_external_base_url()}")
    except Exception as e:
        current_app.logger.error(f"记录请求信息失败: {e}")
